#include "stm32f10x.h"
#include "Encoder.h"
#include "Delay.h"

static volatile int16_t Encoder_Count = 0;      // 编码器增量计数
static volatile int32_t Encoder_Total = 0;      // 编码器总计数
static volatile uint32_t last_interrupt_time = 0; // 上次中断时间
static volatile uint8_t encoder_error_count = 0;  // 编码器错误计数

// 速度计算相关变量
static volatile int32_t last_position = 0;      // 上次位置
static volatile uint32_t last_speed_update = 0; // 上次速度更新时间
static volatile float speed_buffer[ENCODER_SPEED_FILTER]; // 速度滤波缓冲区
static volatile uint8_t speed_buffer_index = 0; // 速度缓冲区索引

/**
 * 函数：旋转编码器初始化
 * 参数：无
 * 返回值：无
 */
void Encoder_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitStructure.GPIO_Pin = ENCODER_PIN_A | ENCODER_PIN_B;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(ENCODER_PORT, &GPIO_InitStructure);

    // AFIO选择中断引脚
    GPIO_EXTILineConfig(GPIO_PortSourceGPIOB, GPIO_PinSource0);
    GPIO_EXTILineConfig(GPIO_PortSourceGPIOB, GPIO_PinSource1);

    // EXTI初始化
    EXTI_InitTypeDef EXTI_InitStructure;
    EXTI_InitStructure.EXTI_Line = ENCODER_EXTI_LINE_A | ENCODER_EXTI_LINE_B;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising_Falling; // 双边沿触发
    EXTI_Init(&EXTI_InitStructure);

    // NVIC配置
    NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = ENCODER_IRQ_A;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = ENCODER_PRIORITY;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_Init(&NVIC_InitStructure);

    NVIC_InitStructure.NVIC_IRQChannel = ENCODER_IRQ_B;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 2;
    NVIC_Init(&NVIC_InitStructure);

    // 初始化变量
    Encoder_Count = 0;
    Encoder_Total = 0;
    encoder_error_count = 0;
    last_interrupt_time = System_GetTick();

    // 初始化速度计算变量
    last_position = 0;
    last_speed_update = System_GetTick();
    speed_buffer_index = 0;
    for(uint8_t i = 0; i < ENCODER_SPEED_FILTER; i++) {
        speed_buffer[i] = 0.0f;
    }

    // 同步全局变量
    g_encoder_position = 0;
    g_actual_speed = 0.0f;
}

/**
 * 函数：旋转编码器获取增量值
 * 参数：无
 * 返回值：自上次调用此函数后，旋转编码器的增量值
 */
int16_t Encoder_Get(void)
{
    int16_t temp;
    __disable_irq();  // 关闭中断，保证原子操作
    temp = Encoder_Count;
    Encoder_Count = 0;
    __enable_irq();   // 开启中断
    return temp;
}

/**
 * 函数：获取编码器总计数
 * 参数：无
 * 返回值：编码器总计数
 */
int16_t Encoder_GetCount(void)
{
    return (int16_t)Encoder_Total;
}

/**
 * 函数：重置编码器计数
 * 参数：无
 * 返回值：无
 */
void Encoder_Reset(void)
{
    __disable_irq();
    Encoder_Count = 0;
    Encoder_Total = 0;
    encoder_error_count = 0;
    __enable_irq();
}

/**
 * 函数：编码器自检
 * 参数：无
 * 返回值：0-正常, 非0-故障
 */
uint8_t Encoder_SelfTest(void)
{
    // 检查编码器引脚状态
    uint8_t pin_a = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_A);
    uint8_t pin_b = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_B);

    // 简单的连通性检查
    if(pin_a == 0 && pin_b == 0) {
        return 1; // 可能短路
    }

    // 检查错误计数
    if(encoder_error_count > 10) {
        encoder_error_count = 0;
        return 2; // 干扰过多
    }

    return 0; // 正常
}

/**
 * 函数：更新速度计算
 * 参数：无
 * 返回值：无
 * 说明：需要定时调用，建议20ms调用一次
 */
void Encoder_UpdateSpeed(void)
{
    uint32_t current_time = System_GetTick();

    // 检查更新间隔
    if((current_time - last_speed_update) < ENCODER_UPDATE_PERIOD) return;

    // 计算时间差(秒)
    float dt = (current_time - last_speed_update) / 1000.0f;
    last_speed_update = current_time;

    // 计算位置差
    int32_t current_position = Encoder_Total;
    int32_t position_diff = current_position - last_position;
    last_position = current_position;

    // 计算瞬时速度 (脉冲/秒 -> RPM)
    float instant_speed = (position_diff / dt) * 60.0f / ENCODER_PPR;

    // 速度滤波
    speed_buffer[speed_buffer_index] = instant_speed;
    speed_buffer_index = (speed_buffer_index + 1) % ENCODER_SPEED_FILTER;

    // 计算平均速度
    float speed_sum = 0.0f;
    for(uint8_t i = 0; i < ENCODER_SPEED_FILTER; i++) {
        speed_sum += speed_buffer[i];
    }

    // 更新全局变量
    g_actual_speed = speed_sum / ENCODER_SPEED_FILTER;
    g_encoder_position = current_position;
}

/**
 * 函数：获取实际速度
 * 参数：无
 * 返回值：实际速度(RPM)
 */
float Encoder_GetSpeed(void)
{
    return g_actual_speed;
}

/**
 * 函数：获取绝对位置
 * 参数：无
 * 返回值：绝对位置(脉冲数)
 */
int32_t Encoder_GetPosition(void)
{
    return g_encoder_position;
}

/**
 * 函数：设置位置
 * 参数：pos - 要设置的位置
 * 返回值：无
 */
void Encoder_SetPosition(int32_t pos)
{
    __disable_irq();
    Encoder_Total = pos;
    g_encoder_position = pos;
    last_position = pos;
    __enable_irq();
}

/**
 * 函数：使能/禁用反馈控制
 * 参数：enable - 1使能, 0禁用
 * 返回值：无
 */
void Encoder_EnableFeedback(uint8_t enable)
{
    g_feedback_enabled = enable;
}

/**
 * 函数：EXTI0外部中断函数
 * 参数：无
 * 返回值：无
 * 注意：此函数为中断函数，无需调用，中断触发后自动执行
 */
void EXTI0_IRQHandler(void)
{
    if (EXTI_GetITStatus(EXTI_Line0) == SET) {
        uint32_t current_time = System_GetTick();

        // 防抖处理：间隔太短的中断忽略
        if((current_time - last_interrupt_time) < 2) {
            encoder_error_count++;
            EXTI_ClearITPendingBit(EXTI_Line0);
            return;
        }
        last_interrupt_time = current_time;

        // 读取两相状态
        uint8_t pin_a = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_A);
        uint8_t pin_b = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_B);

        // 状态机解码
        static uint8_t last_state = 0;
        uint8_t current_state = (pin_a << 1) | pin_b;

        // 根据状态转换判断方向
        if((last_state == 0 && current_state == 1) ||
           (last_state == 1 && current_state == 3) ||
           (last_state == 3 && current_state == 2) ||
           (last_state == 2 && current_state == 0)) {
            Encoder_Count++;
            Encoder_Total++;
        }
        else if((last_state == 0 && current_state == 2) ||
                (last_state == 2 && current_state == 3) ||
                (last_state == 3 && current_state == 1) ||
                (last_state == 1 && current_state == 0)) {
            Encoder_Count--;
            Encoder_Total--;
        }
        else {
            encoder_error_count++; // 无效状态转换
        }

        last_state = current_state;
        EXTI_ClearITPendingBit(EXTI_Line0);
    }
}

/**
 * 函数：EXTI1外部中断函数
 * 参数：无
 * 返回值：无
 * 注意：此函数为中断函数，无需调用，中断触发后自动执行
 */
void EXTI1_IRQHandler(void)
{
    if (EXTI_GetITStatus(EXTI_Line1) == SET) {
        uint32_t current_time = System_GetTick();

        // 防抖处理：间隔太短的中断忽略
        if((current_time - last_interrupt_time) < 2) {
            encoder_error_count++;
            EXTI_ClearITPendingBit(EXTI_Line1);
            return;
        }
        last_interrupt_time = current_time;

        // 读取两相状态
        uint8_t pin_a = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_A);
        uint8_t pin_b = GPIO_ReadInputDataBit(ENCODER_PORT, ENCODER_PIN_B);

        // 状态机解码
        static uint8_t last_state = 0;
        uint8_t current_state = (pin_a << 1) | pin_b;

        // 根据状态转换判断方向
        if((last_state == 0 && current_state == 1) ||
           (last_state == 1 && current_state == 3) ||
           (last_state == 3 && current_state == 2) ||
           (last_state == 2 && current_state == 0)) {
            Encoder_Count++;
            Encoder_Total++;
        }
        else if((last_state == 0 && current_state == 2) ||
                (last_state == 2 && current_state == 3) ||
                (last_state == 3 && current_state == 1) ||
                (last_state == 1 && current_state == 0)) {
            Encoder_Count--;
            Encoder_Total--;
        }
        else {
            encoder_error_count++; // 无效状态转换
        }

        last_state = current_state;
        EXTI_ClearITPendingBit(EXTI_Line1);
    }
}
