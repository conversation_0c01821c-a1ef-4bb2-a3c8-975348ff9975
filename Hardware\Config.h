#ifndef __CONFIG_H
#define __CONFIG_H

#include "stm32f10x.h"

//=============================================================================
// 系统配置参数
//=============================================================================
#define SYSTEM_CLOCK_FREQ       72000000    // 系统时钟频率72MHz
#define SYSTICK_FREQ            1000        // SysTick频率1kHz

//=============================================================================
// 电机控制配置
//=============================================================================
#define MOTOR_PWM_PORT          GPIOA       // PWM输出端口
#define MOTOR_PWM_PIN           GPIO_Pin_2  // PWM输出引脚PA2
#define MOTOR_DIR1_PORT         GPIOA       // 方向控制端口1
#define MOTOR_DIR1_PIN          GPIO_Pin_4  // 方向控制引脚1 PA4
#define MOTOR_DIR2_PORT         GPIOA       // 方向控制端口2
#define MOTOR_DIR2_PIN          GPIO_Pin_5  // 方向控制引脚2 PA5

#define MOTOR_PWM_TIMER         TIM2        // PWM定时器
#define MOTOR_PWM_CHANNEL       3           // PWM通道
#define MOTOR_PWM_FREQ          20000       // PWM频率20kHz
#define MOTOR_PWM_RESOLUTION    100         // PWM分辨率(ARR值)
#define MOTOR_PWM_PRESCALER     36          // PWM预分频器

#define MOTOR_SPEED_MAX         100         // 最大速度值
#define MOTOR_SPEED_MIN         -100        // 最小速度值
#define MOTOR_SPEED_STEP        5           // 速度调节步长
#define MOTOR_ACCEL_TIME        50          // 加速时间(ms)

//=============================================================================
// 编码器配置
//=============================================================================
#define ENCODER_PORT            GPIOB       // 编码器端口
#define ENCODER_PIN_A           GPIO_Pin_0  // 编码器A相引脚PB0
#define ENCODER_PIN_B           GPIO_Pin_1  // 编码器B相引脚PB1
#define ENCODER_EXTI_LINE_A     EXTI_Line0  // A相外部中断线
#define ENCODER_EXTI_LINE_B     EXTI_Line1  // B相外部中断线
#define ENCODER_IRQ_A           EXTI0_IRQn  // A相中断号
#define ENCODER_IRQ_B           EXTI1_IRQn  // B相中断号
#define ENCODER_PRIORITY        1           // 中断优先级

// 编码器反馈控制参数
#define ENCODER_PPR             360         // 编码器每转脉冲数(Pulse Per Revolution)
#define ENCODER_FEEDBACK_ENABLE 1           // 使能编码器反馈控制
#define ENCODER_SPEED_FILTER    5           // 速度滤波系数
#define ENCODER_UPDATE_PERIOD   20          // 编码器更新周期(ms)

// PID控制参数
#define PID_KP                  2.0f        // 比例系数
#define PID_KI                  0.5f        // 积分系数
#define PID_KD                  0.1f        // 微分系数
#define PID_OUTPUT_MAX          100         // PID输出最大值
#define PID_OUTPUT_MIN          -100        // PID输出最小值
#define PID_INTEGRAL_MAX        50          // 积分限幅

//=============================================================================
// 按键配置
//=============================================================================
#define KEY_PORT                GPIOA       // 按键端口
#define KEY1_PIN                GPIO_Pin_11 // 按键1引脚PA11
#define KEY2_PIN                GPIO_Pin_12 // 按键2引脚PA12
#define KEY_DEBOUNCE_TIME       20          // 按键消抖时间(ms)

//=============================================================================
// LED配置
//=============================================================================
#define LED_PORT                GPIOA       // LED端口
#define LED0_PIN                GPIO_Pin_0  // LED0引脚PA0
#define LED1_PIN                GPIO_Pin_1  // LED1引脚PA1
#define LED_BLINK_PERIOD        500         // LED闪烁周期(ms)

//=============================================================================
// OLED显示配置
//=============================================================================
#define OLED_I2C_PORT           GPIOB       // OLED I2C端口
#define OLED_SCL_PIN            GPIO_Pin_6  // OLED SCL引脚PB6
#define OLED_SDA_PIN            GPIO_Pin_7  // OLED SDA引脚PB7
#define OLED_REFRESH_PERIOD     100         // OLED刷新周期(ms)

//=============================================================================
// 蜂鸣器配置
//=============================================================================
#define BUZZER_PORT             GPIOB       // 蜂鸣器端口
#define BUZZER_PIN              GPIO_Pin_12 // 蜂鸣器引脚PB12
#define BUZZER_BEEP_TIME        100         // 蜂鸣时间(ms)

//=============================================================================
// 光敏传感器配置
//=============================================================================
#define LIGHT_SENSOR_PORT       GPIOB       // 光敏传感器端口
#define LIGHT_SENSOR_PIN        GPIO_Pin_13 // 光敏传感器引脚PB13

//=============================================================================
// 温度传感器配置
//=============================================================================
#define DS18B20_PORT            GPIOB       // DS18B20端口
#define DS18B20_PIN             GPIO_Pin_14 // DS18B20引脚PB14
#define TEMP_PROTECT_THRESHOLD  80          // 温度保护阈值(°C)

//=============================================================================
// 系统状态定义
//=============================================================================
typedef enum {
    SYSTEM_OK = 0,
    SYSTEM_ERROR,
    SYSTEM_BUSY,
    SYSTEM_TIMEOUT
} SystemStatus_t;

typedef enum {
    MOTOR_STOP = 0,
    MOTOR_FORWARD,
    MOTOR_BACKWARD,
    MOTOR_BRAKE
} MotorState_t;

typedef enum {
    MODE_MANUAL = 0,    // 手动模式
    MODE_AUTO,          // 自动模式
    MODE_SPEED_TEST,    // 速度测试模式
    MODE_CALIBRATION    // 校准模式
} WorkMode_t;

// PID控制结构体
typedef struct {
    float kp;           // 比例系数
    float ki;           // 积分系数
    float kd;           // 微分系数
    float setpoint;     // 设定值
    float integral;     // 积分累积
    float prev_error;   // 上次误差
    float output;       // 输出值
    float output_max;   // 输出最大值
    float output_min;   // 输出最小值
    float integral_max; // 积分限幅
} PID_t;

//=============================================================================
// 全局变量声明
//=============================================================================
extern volatile int8_t g_motor_speed;          // 当前电机速度
extern volatile int8_t g_target_speed;         // 目标速度
extern volatile MotorState_t g_motor_state;    // 电机状态
extern volatile WorkMode_t g_work_mode;        // 工作模式
extern volatile uint8_t g_system_error;        // 系统错误标志
extern volatile uint32_t g_system_tick;        // 系统滴答计数

// 编码器反馈相关变量
extern volatile float g_actual_speed;          // 实际测量速度
extern volatile int32_t g_encoder_position;    // 编码器位置
extern volatile uint8_t g_feedback_enabled;    // 反馈控制使能标志

//=============================================================================
// 功能宏定义
//=============================================================================
#define ABS(x)                  ((x) > 0 ? (x) : -(x))
#define MAX(a, b)               ((a) > (b) ? (a) : (b))
#define MIN(a, b)               ((a) < (b) ? (a) : (b))
#define CONSTRAIN(x, min, max)  ((x) < (min) ? (min) : ((x) > (max) ? (max) : (x)))

// 错误码定义
#define ERROR_NONE              0x00
#define ERROR_MOTOR_OVERLOAD    0x01
#define ERROR_TEMP_HIGH         0x02
#define ERROR_ENCODER_FAULT     0x04
#define ERROR_COMM_TIMEOUT      0x08

#endif
