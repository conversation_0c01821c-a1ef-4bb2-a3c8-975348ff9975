#include "stm32f10x.h"
#include "Buzzer.h"
#include "System.h"

/**
 * 函数：蜂鸣器初始化
 * 参数：无
 * 返回值：无
 */
void Buzzer_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Pin = BUZZER_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(BUZZER_PORT, &GPIO_InitStructure);

    // 设置初始电平为高(关闭)
    GPIO_SetBits(BUZZER_PORT, BUZZER_PIN);
}

/**
 * 函数：蜂鸣器开启
 * 参数：无
 * 返回值：无
 */
void Buzzer_ON(void)
{
    GPIO_ResetBits(BUZZER_PORT, BUZZER_PIN);
}

/**
 * 函数：蜂鸣器关闭
 * 参数：无
 * 返回值：无
 */
void Buzzer_OFF(void)
{
    GPIO_SetBits(BUZZER_PORT, BUZZER_PIN);
}

/**
 * 函数：蜂鸣器状态翻转
 * 参数：无
 * 返回值：无
 */
void Buzzer_Turn(void)
{
    if (GPIO_ReadOutputDataBit(BUZZER_PORT, BUZZER_PIN) == 0) {
        GPIO_SetBits(BUZZER_PORT, BUZZER_PIN);
    }
    else {
        GPIO_ResetBits(BUZZER_PORT, BUZZER_PIN);
    }
}

/**
 * 函数：蜂鸣器鸣叫指定时间
 * 参数：duration_ms - 鸣叫时间(毫秒)
 * 返回值：无
 */
void Buzzer_Beep(uint16_t duration_ms)
{
    Buzzer_ON();
    System_Delay_ms(duration_ms);
    Buzzer_OFF();
}
