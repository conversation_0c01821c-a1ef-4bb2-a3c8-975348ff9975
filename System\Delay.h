#ifndef __DELAY_H
#define __DELAY_H

#include "stm32f10x.h"

// 延时函数
void Delay_us(uint32_t us);
void Delay_ms(uint32_t ms);
void Delay_s(uint32_t s);

// 系统函数
void System_Init(void);
uint32_t System_GetTick(void);
void System_Delay_ms(uint32_t ms);
uint8_t System_GetError(void);
void System_SetError(uint8_t error_code);
void System_ClearError(uint8_t error_code);
void System_WDT_Init(void);
void System_WDT_Feed(void);

#endif
