.\objects\motor.o: Hardware\Motor.c
.\objects\motor.o: .\Start\stm32f10x.h
.\objects\motor.o: .\Start\core_cm3.h
.\objects\motor.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\motor.o: .\Start\system_stm32f10x.h
.\objects\motor.o: .\User\stm32f10x_conf.h
.\objects\motor.o: .\Library\stm32f10x_adc.h
.\objects\motor.o: .\Start\stm32f10x.h
.\objects\motor.o: .\Library\stm32f10x_bkp.h
.\objects\motor.o: .\Library\stm32f10x_can.h
.\objects\motor.o: .\Library\stm32f10x_cec.h
.\objects\motor.o: .\Library\stm32f10x_crc.h
.\objects\motor.o: .\Library\stm32f10x_dac.h
.\objects\motor.o: .\Library\stm32f10x_dbgmcu.h
.\objects\motor.o: .\Library\stm32f10x_dma.h
.\objects\motor.o: .\Library\stm32f10x_exti.h
.\objects\motor.o: .\Library\stm32f10x_flash.h
.\objects\motor.o: .\Library\stm32f10x_fsmc.h
.\objects\motor.o: .\Library\stm32f10x_gpio.h
.\objects\motor.o: .\Library\stm32f10x_i2c.h
.\objects\motor.o: .\Library\stm32f10x_iwdg.h
.\objects\motor.o: .\Library\stm32f10x_pwr.h
.\objects\motor.o: .\Library\stm32f10x_rcc.h
.\objects\motor.o: .\Library\stm32f10x_rtc.h
.\objects\motor.o: .\Library\stm32f10x_sdio.h
.\objects\motor.o: .\Library\stm32f10x_spi.h
.\objects\motor.o: .\Library\stm32f10x_tim.h
.\objects\motor.o: .\Library\stm32f10x_usart.h
.\objects\motor.o: .\Library\stm32f10x_wwdg.h
.\objects\motor.o: .\Library\misc.h
.\objects\motor.o: Hardware\Motor.h
.\objects\motor.o: Hardware\Config.h
.\objects\motor.o: Hardware\PWM.h
.\objects\motor.o: .\System\System.h
