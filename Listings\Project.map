Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    delay.o(i.SysTick_Increment) refers to delay.o(.data) for g_system_tick
    delay.o(i.System_ClearError) refers to delay.o(.data) for g_system_error
    delay.o(i.System_Delay_ms) refers to delay.o(i.Delay_ms) for Delay_ms
    delay.o(i.System_GetError) refers to delay.o(.data) for g_system_error
    delay.o(i.System_GetTick) refers to delay.o(.data) for g_system_tick
    delay.o(i.System_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    delay.o(i.System_Init) refers to delay.o(.data) for system_initialized
    delay.o(i.System_Init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.System_SetError) refers to delay.o(.data) for g_system_error
    led.o(i.LED0_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED0_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED1_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    key.o(i.Key_GetNum) refers to delay.o(i.System_GetTick) for System_GetTick
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to key.o(.data) for key_state
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_IsPressed) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pwm.o(i.PWM_Disable) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_Enable) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_GetCompare3) refers to stm32f10x_tim.o(i.TIM_GetCapture3) for TIM_GetCapture3
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_Init) refers to pwm.o(.data) for pwm_period
    pwm.o(i.PWM_SetCompare3) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    pwm.o(i.PWM_SetFrequency) refers to stm32f10x_tim.o(i.TIM_PrescalerConfig) for TIM_PrescalerConfig
    pwm.o(i.PWM_SetFrequency) refers to pwm.o(.data) for pwm_prescaler
    motor.o(i.Motor_Brake) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_Brake) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_Brake) refers to delay.o(.data) for g_motor_speed
    motor.o(i.Motor_Disable) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_Disable) refers to pwm.o(i.PWM_Disable) for PWM_Disable
    motor.o(i.Motor_Disable) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_Enable) refers to pwm.o(i.PWM_Enable) for PWM_Enable
    motor.o(i.Motor_Enable) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_GetSpeed) refers to delay.o(.data) for g_motor_speed
    motor.o(i.Motor_GetState) refers to delay.o(.data) for g_motor_state
    motor.o(i.Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Motor_Init) refers to pwm.o(i.PWM_Init) for PWM_Init
    motor.o(i.Motor_Init) refers to delay.o(i.System_GetTick) for System_GetTick
    motor.o(i.Motor_Init) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_Init) refers to delay.o(.data) for g_motor_speed
    motor.o(i.Motor_Init) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_SelfTest) refers to motor.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor.o(i.Motor_SelfTest) refers to delay.o(i.System_Delay_ms) for System_Delay_ms
    motor.o(i.Motor_SelfTest) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_SetSpeed) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_SetSpeed) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_SetSpeed) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_SetSpeed) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_SetSpeed) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_SetSpeed) refers to delay.o(.data) for g_motor_speed
    motor.o(i.Motor_SetTargetSpeed) refers to delay.o(.data) for g_target_speed
    motor.o(i.Motor_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Stop) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_Stop) refers to delay.o(.data) for g_motor_speed
    motor.o(i.Motor_Update) refers to delay.o(i.System_GetTick) for System_GetTick
    motor.o(i.Motor_Update) refers to motor.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor.o(i.Motor_Update) refers to motor.o(.data) for last_update_tick
    motor.o(i.Motor_Update) refers to delay.o(.data) for g_motor_speed
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI0_IRQHandler) refers to delay.o(i.System_GetTick) for System_GetTick
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI0_IRQHandler) refers to encoder.o(.data) for last_interrupt_time
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI1_IRQHandler) refers to delay.o(i.System_GetTick) for System_GetTick
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI1_IRQHandler) refers to encoder.o(.data) for last_interrupt_time
    encoder.o(i.Encoder_Get) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_GetCount) refers to encoder.o(.data) for Encoder_Total
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    encoder.o(i.Encoder_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.Encoder_Init) refers to delay.o(i.System_GetTick) for System_GetTick
    encoder.o(i.Encoder_Init) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_Reset) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_SelfTest) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.Encoder_SelfTest) refers to encoder.o(.data) for encoder_error_count
    buzzer.o(i.Buzzer_Beep) refers to buzzer.o(i.Buzzer_ON) for Buzzer_ON
    buzzer.o(i.Buzzer_Beep) refers to delay.o(i.System_Delay_ms) for System_Delay_ms
    buzzer.o(i.Buzzer_Beep) refers to buzzer.o(i.Buzzer_OFF) for Buzzer_OFF
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    lightsensor.o(i.LightSensor_Get) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    lightsensor.o(i.LightSensor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lightsensor.o(i.LightSensor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Check) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Check) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Check) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ds18b20.o(i.DS18B20_GetTemperature) refers to ds18b20.o(i.DS18B20_Get_Temp) for DS18B20_Get_Temp
    ds18b20.o(i.DS18B20_GetTemperature) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    ds18b20.o(i.DS18B20_GetTemperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Start) for DS18B20_Start
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Read_Byte) for DS18B20_Read_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    ds18b20.o(i.DS18B20_Get_Temp) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ds18b20.o(i.DS18B20_Get_Temp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ds18b20.o(i.DS18B20_Get_Temp) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Mode) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ds18b20.o(i.DS18B20_Mode) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Read_Bit) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Read_Bit) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ds18b20.o(i.DS18B20_Read_Byte) refers to ds18b20.o(i.DS18B20_Read_Bit) for DS18B20_Read_Bit
    ds18b20.o(i.DS18B20_Rst) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Rst) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Rst) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Rst) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Write_Byte) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Write_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Write_Byte) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Write_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.Display_MainPage) refers to strcpy.o(.text) for strcpy
    main.o(i.Display_MainPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_MainPage) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.Display_MainPage) refers to delay.o(.data) for g_work_mode
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_SettingsPage) refers to delay.o(i.System_GetError) for System_GetError
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowHexNum) for OLED_ShowHexNum
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Display_StatusPage) refers to ds18b20.o(i.DS18B20_GetTemperature) for DS18B20_GetTemperature
    main.o(i.Display_StatusPage) refers to lightsensor.o(i.LightSensor_Get) for LightSensor_Get
    main.o(i.Display_StatusPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_StatusPage) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    main.o(i.Display_StatusPage) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Display_StatusPage) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    main.o(i.Display_StatusPage) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    main.o(i.Display_StatusPage) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.Display_StatusPage) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.Handle_EncoderInput) refers to encoder.o(i.Encoder_Get) for Encoder_Get
    main.o(i.Handle_EncoderInput) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_EncoderInput) refers to delay.o(.data) for g_work_mode
    main.o(i.Handle_KeyInput) refers to key.o(i.Key_GetNum) for Key_GetNum
    main.o(i.Handle_KeyInput) refers to buzzer.o(i.Buzzer_Beep) for Buzzer_Beep
    main.o(i.Handle_KeyInput) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_KeyInput) refers to key.o(i.Key_IsPressed) for Key_IsPressed
    main.o(i.Handle_KeyInput) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.Handle_KeyInput) refers to motor.o(i.Motor_Stop) for Motor_Stop
    main.o(i.Handle_KeyInput) refers to main.o(.data) for KeyNum
    main.o(i.Handle_KeyInput) refers to delay.o(.data) for g_work_mode
    main.o(i.Handle_WorkMode) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.Handle_WorkMode) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_WorkMode) refers to motor.o(i.Motor_SelfTest) for Motor_SelfTest
    main.o(i.Handle_WorkMode) refers to delay.o(.data) for g_work_mode
    main.o(i.Handle_WorkMode) refers to main.o(.data) for mode_timer
    main.o(i.System_Init_All) refers to delay.o(i.System_Init) for System_Init
    main.o(i.System_Init_All) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.System_Init_All) refers to motor.o(i.Motor_Init) for Motor_Init
    main.o(i.System_Init_All) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.System_Init_All) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    main.o(i.System_Init_All) refers to lightsensor.o(i.LightSensor_Init) for LightSensor_Init
    main.o(i.System_Init_All) refers to buzzer.o(i.Buzzer_Init) for Buzzer_Init
    main.o(i.System_Init_All) refers to ds18b20.o(i.DS18B20_Init) for DS18B20_Init
    main.o(i.System_Init_All) refers to delay.o(i.System_WDT_Init) for System_WDT_Init
    main.o(i.System_Init_All) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.System_Init_All) refers to main.o(.data) for last_display_update
    main.o(i.Update_Display) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.Update_Display) refers to main.o(i.Display_MainPage) for Display_MainPage
    main.o(i.Update_Display) refers to main.o(i.Display_StatusPage) for Display_StatusPage
    main.o(i.Update_Display) refers to main.o(i.Display_SettingsPage) for Display_SettingsPage
    main.o(i.Update_Display) refers to main.o(.data) for last_display_update
    main.o(i.Update_LED_Status) refers to delay.o(i.System_GetTick) for System_GetTick
    main.o(i.Update_LED_Status) refers to led.o(i.LED0_OFF) for LED0_OFF
    main.o(i.Update_LED_Status) refers to led.o(i.LED0_Turn) for LED0_Turn
    main.o(i.Update_LED_Status) refers to delay.o(i.System_GetError) for System_GetError
    main.o(i.Update_LED_Status) refers to led.o(i.LED1_Turn) for LED1_Turn
    main.o(i.Update_LED_Status) refers to main.o(.data) for last_led_update
    main.o(i.Update_LED_Status) refers to delay.o(.data) for g_motor_state
    main.o(i.main) refers to main.o(i.System_Init_All) for System_Init_All
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to buzzer.o(i.Buzzer_Beep) for Buzzer_Beep
    main.o(i.main) refers to delay.o(i.System_Delay_ms) for System_Delay_ms
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to main.o(i.Handle_KeyInput) for Handle_KeyInput
    main.o(i.main) refers to main.o(i.Handle_EncoderInput) for Handle_EncoderInput
    main.o(i.main) refers to main.o(i.Handle_WorkMode) for Handle_WorkMode
    main.o(i.main) refers to motor.o(i.Motor_Update) for Motor_Update
    main.o(i.main) refers to main.o(i.Update_Display) for Update_Display
    main.o(i.main) refers to delay.o(i.System_WDT_Feed) for System_WDT_Feed
    stm32f10x_it.o(i.SysTick_Handler) refers to delay.o(i.SysTick_Increment) for SysTick_Increment
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (216 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing delay.o(i.System_ClearError), (16 bytes).
    Removing delay.o(i.System_SetError), (16 bytes).
    Removing led.o(i.LED0_OFF), (16 bytes).
    Removing led.o(i.LED0_ON), (16 bytes).
    Removing led.o(i.LED0_Turn), (36 bytes).
    Removing led.o(i.LED1_OFF), (16 bytes).
    Removing led.o(i.LED1_ON), (16 bytes).
    Removing led.o(i.LED1_Turn), (36 bytes).
    Removing led.o(i.LED_Init), (48 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing pwm.o(i.PWM_Disable), (14 bytes).
    Removing pwm.o(i.PWM_Enable), (12 bytes).
    Removing pwm.o(i.PWM_GetCompare3), (12 bytes).
    Removing pwm.o(i.PWM_SetFrequency), (48 bytes).
    Removing motor.o(i.Motor_Brake), (60 bytes).
    Removing motor.o(i.Motor_Disable), (24 bytes).
    Removing motor.o(i.Motor_Enable), (20 bytes).
    Removing motor.o(i.Motor_GetSpeed), (12 bytes).
    Removing motor.o(i.Motor_GetState), (12 bytes).
    Removing encoder.o(i.Encoder_GetCount), (12 bytes).
    Removing encoder.o(i.Encoder_Reset), (32 bytes).
    Removing encoder.o(i.Encoder_SelfTest), (60 bytes).
    Removing buzzer.o(i.Buzzer_Turn), (44 bytes).
    Removing main.o(i.Update_LED_Status), (100 bytes).

481 unused section(s) (total 18998 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    Hardware\Buzzer.c                        0x00000000   Number         0  buzzer.o ABSOLUTE
    Hardware\Encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\Key.c                           0x00000000   Number         0  key.o ABSOLUTE
    Hardware\LED.c                           0x00000000   Number         0  led.o ABSOLUTE
    Hardware\LightSensor.c                   0x00000000   Number         0  lightsensor.o ABSOLUTE
    Hardware\Motor.c                         0x00000000   Number         0  motor.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\PWM.c                           0x00000000   Number         0  pwm.o ABSOLUTE
    Hardware\ds18b20.c                       0x00000000   Number         0  ds18b20.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section        0  strcpy.o(.text)
    .text                                    0x08000210   Section        0  heapauxi.o(.text)
    .text                                    0x08000216   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000260   Section        0  exit.o(.text)
    .text                                    0x08000274   Section        8  libspace.o(.text)
    .text                                    0x0800027c   Section        0  sys_exit.o(.text)
    .text                                    0x08000288   Section        2  use_no_semi.o(.text)
    .text                                    0x0800028a   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800028a   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Buzzer_Beep                            0x0800028e   Section        0  buzzer.o(i.Buzzer_Beep)
    i.Buzzer_Init                            0x080002a4   Section        0  buzzer.o(i.Buzzer_Init)
    i.Buzzer_OFF                             0x080002d8   Section        0  buzzer.o(i.Buzzer_OFF)
    i.Buzzer_ON                              0x080002ec   Section        0  buzzer.o(i.Buzzer_ON)
    i.DS18B20_Check                          0x08000300   Section        0  ds18b20.o(i.DS18B20_Check)
    i.DS18B20_GetTemperature                 0x0800035c   Section        0  ds18b20.o(i.DS18B20_GetTemperature)
    i.DS18B20_Get_Temp                       0x08000378   Section        0  ds18b20.o(i.DS18B20_Get_Temp)
    i.DS18B20_Init                           0x08000404   Section        0  ds18b20.o(i.DS18B20_Init)
    i.DS18B20_Mode                           0x08000444   Section        0  ds18b20.o(i.DS18B20_Mode)
    i.DS18B20_Read_Bit                       0x08000484   Section        0  ds18b20.o(i.DS18B20_Read_Bit)
    i.DS18B20_Read_Byte                      0x080004d4   Section        0  ds18b20.o(i.DS18B20_Read_Byte)
    i.DS18B20_Rst                            0x080004f8   Section        0  ds18b20.o(i.DS18B20_Rst)
    i.DS18B20_Start                          0x08000528   Section        0  ds18b20.o(i.DS18B20_Start)
    i.DS18B20_Write_Byte                     0x08000540   Section        0  ds18b20.o(i.DS18B20_Write_Byte)
    i.DebugMon_Handler                       0x080005a8   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.Delay_ms                               0x080005aa   Section        0  delay.o(i.Delay_ms)
    i.Delay_us                               0x080005c2   Section        0  delay.o(i.Delay_us)
    i.Display_MainPage                       0x080005f0   Section        0  main.o(i.Display_MainPage)
    i.Display_SettingsPage                   0x0800073c   Section        0  main.o(i.Display_SettingsPage)
    i.Display_StatusPage                     0x080007c0   Section        0  main.o(i.Display_StatusPage)
    i.EXTI0_IRQHandler                       0x080008bc   Section        0  encoder.o(i.EXTI0_IRQHandler)
    i.EXTI1_IRQHandler                       0x080009c8   Section        0  encoder.o(i.EXTI1_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x08000ad4   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08000ae0   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08000b08   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.Encoder_Get                            0x08000b9c   Section        0  encoder.o(i.Encoder_Get)
    i.Encoder_Init                           0x08000bb4   Section        0  encoder.o(i.Encoder_Init)
    i.GPIO_EXTILineConfig                    0x08000c64   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08000ca4   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x08000dba   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000dcc   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08000dd0   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08000dd4   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Handle_EncoderInput                    0x08000de0   Section        0  main.o(i.Handle_EncoderInput)
    i.Handle_KeyInput                        0x08000e64   Section        0  main.o(i.Handle_KeyInput)
    i.Handle_WorkMode                        0x08000f14   Section        0  main.o(i.Handle_WorkMode)
    i.HardFault_Handler                      0x08000fa4   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Key_GetNum                             0x08000fa8   Section        0  key.o(i.Key_GetNum)
    i.Key_Init                               0x08001054   Section        0  key.o(i.Key_Init)
    i.Key_IsPressed                          0x08001080   Section        0  key.o(i.Key_IsPressed)
    i.LightSensor_Get                        0x080010b8   Section        0  lightsensor.o(i.LightSensor_Get)
    i.LightSensor_Init                       0x080010cc   Section        0  lightsensor.o(i.LightSensor_Init)
    i.MemManage_Handler                      0x080010f8   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x080010fc   Section        0  motor.o(i.Motor_Init)
    i.Motor_SelfTest                         0x08001160   Section        0  motor.o(i.Motor_SelfTest)
    i.Motor_SetSpeed                         0x08001184   Section        0  motor.o(i.Motor_SetSpeed)
    i.Motor_SetTargetSpeed                   0x0800120c   Section        0  motor.o(i.Motor_SetTargetSpeed)
    i.Motor_Stop                             0x0800122c   Section        0  motor.o(i.Motor_Stop)
    i.Motor_Update                           0x08001264   Section        0  motor.o(i.Motor_Update)
    i.NMI_Handler                            0x08001304   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001308   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001378   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x0800138c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_I2C_Init                          0x080013b8   Section        0  oled.o(i.OLED_I2C_Init)
    i.OLED_I2C_SendByte                      0x08001408   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x0800145c   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x08001490   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080014b8   Section        0  oled.o(i.OLED_Init)
    i.OLED_Pow                               0x08001566   Section        0  oled.o(i.OLED_Pow)
    i.OLED_SetCursor                         0x0800157a   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x0800159c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowHexNum                        0x08001610   Section        0  oled.o(i.OLED_ShowHexNum)
    i.OLED_ShowNum                           0x08001664   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_ShowSignedNum                     0x080016a8   Section        0  oled.o(i.OLED_ShowSignedNum)
    i.OLED_ShowString                        0x0800170e   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WriteCommand                      0x08001736   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08001756   Section        0  oled.o(i.OLED_WriteData)
    i.PWM_Init                               0x08001778   Section        0  pwm.o(i.PWM_Init)
    i.PWM_SetCompare3                        0x08001818   Section        0  pwm.o(i.PWM_SetCompare3)
    i.PendSV_Handler                         0x0800183c   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x08001840   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001860   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.SVC_Handler                            0x08001880   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001882   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08001883   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x0800188c   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x0800188d   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x0800196c   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SysTick_Increment                      0x08001974   Section        0  delay.o(i.SysTick_Increment)
    i.SystemInit                             0x08001984   Section        0  system_stm32f10x.o(i.SystemInit)
    i.System_Delay_ms                        0x080019e4   Section        0  delay.o(i.System_Delay_ms)
    i.System_GetError                        0x080019f0   Section        0  delay.o(i.System_GetError)
    i.System_GetTick                         0x080019fc   Section        0  delay.o(i.System_GetTick)
    i.System_Init                            0x08001a08   Section        0  delay.o(i.System_Init)
    i.System_Init_All                        0x08001ab8   Section        0  main.o(i.System_Init_All)
    i.System_WDT_Feed                        0x08001af8   Section        0  delay.o(i.System_WDT_Feed)
    i.System_WDT_Init                        0x08001afa   Section        0  delay.o(i.System_WDT_Init)
    i.TIM_Cmd                                0x08001afc   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_InternalClockConfig                0x08001b14   Section        0  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    i.TIM_OC3Init                            0x08001b20   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OCStructInit                       0x08001bc0   Section        0  stm32f10x_tim.o(i.TIM_OCStructInit)
    i.TIM_SetCompare3                        0x08001bd4   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_TimeBaseInit                       0x08001bd8   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Update_Display                         0x08001c7c   Section        0  main.o(i.Update_Display)
    i.UsageFault_Handler                     0x08001cc4   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.main                                   0x08001cc8   Section        0  main.o(i.main)
    x$fpl$dfix                               0x08001d34   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dmul                               0x08001d94   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08001ee8   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08001f84   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08001f90   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08001fe8   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08001ff7   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fdiv                               0x080020ac   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080020ad   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$ffixu                              0x08002230   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08002270   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x080022a0   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fmul                               0x080022c8   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x080023ca   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08002456   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frsb                               0x08002460   Section       20  faddsub_clz.o(x$fpl$frsb)
    x$fpl$fsub                               0x08002474   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08002483   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    .constdata                               0x0800255e   Section     1520  oled.o(.constdata)
    x$fpl$usenofp                            0x0800255e   Section        0  usenofp.o(x$fpl$usenofp)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       13  delay.o(.data)
    system_initialized                       0x20000020   Data           1  delay.o(.data)
    .data                                    0x20000024   Section       12  key.o(.data)
    key_state                                0x20000024   Data           2  key.o(.data)
    key_time                                 0x20000028   Data           8  key.o(.data)
    .data                                    0x20000030   Section        4  pwm.o(.data)
    pwm_period                               0x20000030   Data           2  pwm.o(.data)
    pwm_prescaler                            0x20000032   Data           2  pwm.o(.data)
    .data                                    0x20000034   Section        8  motor.o(.data)
    motor_enabled                            0x20000034   Data           1  motor.o(.data)
    last_update_tick                         0x20000038   Data           4  motor.o(.data)
    .data                                    0x2000003c   Section       15  encoder.o(.data)
    Encoder_Count                            0x2000003c   Data           2  encoder.o(.data)
    Encoder_Total                            0x20000040   Data           4  encoder.o(.data)
    last_interrupt_time                      0x20000044   Data           4  encoder.o(.data)
    encoder_error_count                      0x20000048   Data           1  encoder.o(.data)
    last_state                               0x20000049   Data           1  encoder.o(.data)
    last_state                               0x2000004a   Data           1  encoder.o(.data)
    .data                                    0x2000004c   Section       25  main.o(.data)
    KeyNum                                   0x2000004c   Data           1  main.o(.data)
    last_display_update                      0x20000050   Data           4  main.o(.data)
    last_led_update                          0x20000054   Data           4  main.o(.data)
    display_page                             0x20000058   Data           1  main.o(.data)
    auto_mode_direction                      0x20000059   Data           1  main.o(.data)
    press_start                              0x2000005c   Data           4  main.o(.data)
    mode_timer                               0x20000060   Data           4  main.o(.data)
    heartbeat_count                          0x20000064   Data           1  main.o(.data)
    .bss                                     0x20000068   Section       96  libspace.o(.bss)
    HEAP                                     0x200000c8   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200000c8   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200002c8   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200002c8   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x200006c8   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART1_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    strcpy                                   0x080001c9   Thumb Code    72  strcpy.o(.text)
    __use_two_region_memory                  0x08000211   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000213   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000215   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x08000217   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000261   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000275   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000275   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000275   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x0800027d   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000289   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000289   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800028b   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800028b   Thumb Code     0  indicate_semi.o(.text)
    Buzzer_Beep                              0x0800028f   Thumb Code    20  buzzer.o(i.Buzzer_Beep)
    Buzzer_Init                              0x080002a5   Thumb Code    48  buzzer.o(i.Buzzer_Init)
    Buzzer_OFF                               0x080002d9   Thumb Code    14  buzzer.o(i.Buzzer_OFF)
    Buzzer_ON                                0x080002ed   Thumb Code    14  buzzer.o(i.Buzzer_ON)
    DS18B20_Check                            0x08000301   Thumb Code    88  ds18b20.o(i.DS18B20_Check)
    DS18B20_GetTemperature                   0x0800035d   Thumb Code    24  ds18b20.o(i.DS18B20_GetTemperature)
    DS18B20_Get_Temp                         0x08000379   Thumb Code   136  ds18b20.o(i.DS18B20_Get_Temp)
    DS18B20_Init                             0x08000405   Thumb Code    58  ds18b20.o(i.DS18B20_Init)
    DS18B20_Mode                             0x08000445   Thumb Code    60  ds18b20.o(i.DS18B20_Mode)
    DS18B20_Read_Bit                         0x08000485   Thumb Code    74  ds18b20.o(i.DS18B20_Read_Bit)
    DS18B20_Read_Byte                        0x080004d5   Thumb Code    34  ds18b20.o(i.DS18B20_Read_Byte)
    DS18B20_Rst                              0x080004f9   Thumb Code    44  ds18b20.o(i.DS18B20_Rst)
    DS18B20_Start                            0x08000529   Thumb Code    24  ds18b20.o(i.DS18B20_Start)
    DS18B20_Write_Byte                       0x08000541   Thumb Code    98  ds18b20.o(i.DS18B20_Write_Byte)
    DebugMon_Handler                         0x080005a9   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    Delay_ms                                 0x080005ab   Thumb Code    24  delay.o(i.Delay_ms)
    Delay_us                                 0x080005c3   Thumb Code    46  delay.o(i.Delay_us)
    Display_MainPage                         0x080005f1   Thumb Code   218  main.o(i.Display_MainPage)
    Display_SettingsPage                     0x0800073d   Thumb Code    84  main.o(i.Display_SettingsPage)
    Display_StatusPage                       0x080007c1   Thumb Code   194  main.o(i.Display_StatusPage)
    EXTI0_IRQHandler                         0x080008bd   Thumb Code   242  encoder.o(i.EXTI0_IRQHandler)
    EXTI1_IRQHandler                         0x080009c9   Thumb Code   242  encoder.o(i.EXTI1_IRQHandler)
    EXTI_ClearITPendingBit                   0x08000ad5   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08000ae1   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08000b09   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    Encoder_Get                              0x08000b9d   Thumb Code    18  encoder.o(i.Encoder_Get)
    Encoder_Init                             0x08000bb5   Thumb Code   154  encoder.o(i.Encoder_Init)
    GPIO_EXTILineConfig                      0x08000c65   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08000ca5   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08000dbb   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000dcd   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08000dd1   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08000dd5   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Handle_EncoderInput                      0x08000de1   Thumb Code   122  main.o(i.Handle_EncoderInput)
    Handle_KeyInput                          0x08000e65   Thumb Code   158  main.o(i.Handle_KeyInput)
    Handle_WorkMode                          0x08000f15   Thumb Code   128  main.o(i.Handle_WorkMode)
    HardFault_Handler                        0x08000fa5   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Key_GetNum                               0x08000fa9   Thumb Code   158  key.o(i.Key_GetNum)
    Key_Init                                 0x08001055   Thumb Code    40  key.o(i.Key_Init)
    Key_IsPressed                            0x08001081   Thumb Code    52  key.o(i.Key_IsPressed)
    LightSensor_Get                          0x080010b9   Thumb Code    14  lightsensor.o(i.LightSensor_Get)
    LightSensor_Init                         0x080010cd   Thumb Code    40  lightsensor.o(i.LightSensor_Init)
    MemManage_Handler                        0x080010f9   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    Motor_Init                               0x080010fd   Thumb Code    74  motor.o(i.Motor_Init)
    Motor_SelfTest                           0x08001161   Thumb Code    36  motor.o(i.Motor_SelfTest)
    Motor_SetSpeed                           0x08001185   Thumb Code   118  motor.o(i.Motor_SetSpeed)
    Motor_SetTargetSpeed                     0x0800120d   Thumb Code    28  motor.o(i.Motor_SetTargetSpeed)
    Motor_Stop                               0x0800122d   Thumb Code    40  motor.o(i.Motor_Stop)
    Motor_Update                             0x08001265   Thumb Code   148  motor.o(i.Motor_Update)
    NMI_Handler                              0x08001305   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001309   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001379   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x0800138d   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_I2C_Init                            0x080013b9   Thumb Code    76  oled.o(i.OLED_I2C_Init)
    OLED_I2C_SendByte                        0x08001409   Thumb Code    80  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x0800145d   Thumb Code    48  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x08001491   Thumb Code    36  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080014b9   Thumb Code   174  oled.o(i.OLED_Init)
    OLED_Pow                                 0x08001567   Thumb Code    20  oled.o(i.OLED_Pow)
    OLED_SetCursor                           0x0800157b   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x0800159d   Thumb Code   110  oled.o(i.OLED_ShowChar)
    OLED_ShowHexNum                          0x08001611   Thumb Code    84  oled.o(i.OLED_ShowHexNum)
    OLED_ShowNum                             0x08001665   Thumb Code    68  oled.o(i.OLED_ShowNum)
    OLED_ShowSignedNum                       0x080016a9   Thumb Code   102  oled.o(i.OLED_ShowSignedNum)
    OLED_ShowString                          0x0800170f   Thumb Code    40  oled.o(i.OLED_ShowString)
    OLED_WriteCommand                        0x08001737   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08001757   Thumb Code    32  oled.o(i.OLED_WriteData)
    PWM_Init                                 0x08001779   Thumb Code   148  pwm.o(i.PWM_Init)
    PWM_SetCompare3                          0x08001819   Thumb Code    36  pwm.o(i.PWM_SetCompare3)
    PendSV_Handler                           0x0800183d   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x08001841   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001861   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    SVC_Handler                              0x08001881   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800196d   Thumb Code     8  stm32f10x_it.o(i.SysTick_Handler)
    SysTick_Increment                        0x08001975   Thumb Code    12  delay.o(i.SysTick_Increment)
    SystemInit                               0x08001985   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    System_Delay_ms                          0x080019e5   Thumb Code    12  delay.o(i.System_Delay_ms)
    System_GetError                          0x080019f1   Thumb Code     6  delay.o(i.System_GetError)
    System_GetTick                           0x080019fd   Thumb Code     6  delay.o(i.System_GetTick)
    System_Init                              0x08001a09   Thumb Code   134  delay.o(i.System_Init)
    System_Init_All                          0x08001ab9   Thumb Code    56  main.o(i.System_Init_All)
    System_WDT_Feed                          0x08001af9   Thumb Code     2  delay.o(i.System_WDT_Feed)
    System_WDT_Init                          0x08001afb   Thumb Code     2  delay.o(i.System_WDT_Init)
    TIM_Cmd                                  0x08001afd   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_InternalClockConfig                  0x08001b15   Thumb Code    12  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    TIM_OC3Init                              0x08001b21   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OCStructInit                         0x08001bc1   Thumb Code    20  stm32f10x_tim.o(i.TIM_OCStructInit)
    TIM_SetCompare3                          0x08001bd5   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_TimeBaseInit                         0x08001bd9   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Update_Display                           0x08001c7d   Thumb Code    64  main.o(i.Update_Display)
    UsageFault_Handler                       0x08001cc5   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    main                                     0x08001cc9   Thumb Code    76  main.o(i.main)
    __aeabi_d2iz                             0x08001d35   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08001d35   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_dmul                             0x08001d95   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08001d95   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08001ee9   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08001f85   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08001f91   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08001f91   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08001fe9   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08001fe9   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_fdiv                             0x080020ad   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080020ad   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_f2uiz                            0x08002231   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08002231   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08002271   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08002271   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x080022a1   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x080022a1   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_fmul                             0x080022c9   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080022c9   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x080023cb   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08002457   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_frsub                            0x08002461   Thumb Code     0  faddsub_clz.o(x$fpl$frsb)
    _frsb                                    0x08002461   Thumb Code    20  faddsub_clz.o(x$fpl$frsb)
    __aeabi_fsub                             0x08002475   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08002475   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    OLED_F8x16                               0x0800255e   Data        1520  oled.o(.constdata)
    __I$use$fp                               0x0800255e   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08002b50   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08002b70   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    g_motor_speed                            0x20000014   Data           1  delay.o(.data)
    g_target_speed                           0x20000015   Data           1  delay.o(.data)
    g_motor_state                            0x20000016   Data           1  delay.o(.data)
    g_work_mode                              0x20000017   Data           1  delay.o(.data)
    g_system_error                           0x20000018   Data           1  delay.o(.data)
    g_system_tick                            0x2000001c   Data           4  delay.o(.data)
    __libspace_start                         0x20000068   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000c8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002bd8, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00002b70, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         4035  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x00000034   Code   RO         4231    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x08000128   0x0000001a   Code   RO         4233    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x08000142   0x00000002   PAD
    0x08000144   0x08000144   0x0000001c   Code   RO         4235    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x08000160   0x00000002   Code   RO         4101    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4108    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4110    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4113    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4115    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4117    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4120    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4122    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4124    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4126    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4128    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4130    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4132    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4134    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4136    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4138    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4140    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4144    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4146    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4148    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000000   Code   RO         4150    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x08000162   0x00000002   Code   RO         4151    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x08000164   0x00000002   Code   RO         4171    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4184    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4186    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4189    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4192    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4194    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000000   Code   RO         4197    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000166   0x08000166   0x00000002   Code   RO         4198    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000168   0x08000168   0x00000000   Code   RO         4067    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x08000168   0x00000000   Code   RO         4078    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x08000168   0x00000006   Code   RO         4090    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x0800016e   0x00000000   Code   RO         4080    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x0800016e   0x00000004   Code   RO         4081    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000000   Code   RO         4083    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x08000172   0x00000008   Code   RO         4084    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x0800017a   0x00000002   Code   RO         4105    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x0800017c   0x00000000   Code   RO         4153    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x0800017c   0x00000004   Code   RO         4154    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x08000180   0x00000006   Code   RO         4155    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001c8   0x080001c8   0x00000048   Code   RO         4031    .text               c_w.l(strcpy.o)
    0x08000210   0x08000210   0x00000006   Code   RO         4033    .text               c_w.l(heapauxi.o)
    0x08000216   0x08000216   0x0000004a   Code   RO         4092    .text               c_w.l(sys_stackheap_outer.o)
    0x08000260   0x08000260   0x00000012   Code   RO         4094    .text               c_w.l(exit.o)
    0x08000272   0x08000272   0x00000002   PAD
    0x08000274   0x08000274   0x00000008   Code   RO         4102    .text               c_w.l(libspace.o)
    0x0800027c   0x0800027c   0x0000000c   Code   RO         4163    .text               c_w.l(sys_exit.o)
    0x08000288   0x08000288   0x00000002   Code   RO         4174    .text               c_w.l(use_no_semi.o)
    0x0800028a   0x0800028a   0x00000000   Code   RO         4176    .text               c_w.l(indicate_semi.o)
    0x0800028a   0x0800028a   0x00000004   Code   RO         3966    i.BusFault_Handler  stm32f10x_it.o
    0x0800028e   0x0800028e   0x00000014   Code   RO         3735    i.Buzzer_Beep       buzzer.o
    0x080002a2   0x080002a2   0x00000002   PAD
    0x080002a4   0x080002a4   0x00000034   Code   RO         3736    i.Buzzer_Init       buzzer.o
    0x080002d8   0x080002d8   0x00000014   Code   RO         3737    i.Buzzer_OFF        buzzer.o
    0x080002ec   0x080002ec   0x00000014   Code   RO         3738    i.Buzzer_ON         buzzer.o
    0x08000300   0x08000300   0x0000005c   Code   RO         3792    i.DS18B20_Check     ds18b20.o
    0x0800035c   0x0800035c   0x0000001c   Code   RO         3793    i.DS18B20_GetTemperature  ds18b20.o
    0x08000378   0x08000378   0x0000008c   Code   RO         3794    i.DS18B20_Get_Temp  ds18b20.o
    0x08000404   0x08000404   0x00000040   Code   RO         3795    i.DS18B20_Init      ds18b20.o
    0x08000444   0x08000444   0x00000040   Code   RO         3796    i.DS18B20_Mode      ds18b20.o
    0x08000484   0x08000484   0x00000050   Code   RO         3797    i.DS18B20_Read_Bit  ds18b20.o
    0x080004d4   0x080004d4   0x00000022   Code   RO         3798    i.DS18B20_Read_Byte  ds18b20.o
    0x080004f6   0x080004f6   0x00000002   PAD
    0x080004f8   0x080004f8   0x00000030   Code   RO         3799    i.DS18B20_Rst       ds18b20.o
    0x08000528   0x08000528   0x00000018   Code   RO         3800    i.DS18B20_Start     ds18b20.o
    0x08000540   0x08000540   0x00000068   Code   RO         3801    i.DS18B20_Write_Byte  ds18b20.o
    0x080005a8   0x080005a8   0x00000002   Code   RO         3967    i.DebugMon_Handler  stm32f10x_it.o
    0x080005aa   0x080005aa   0x00000018   Code   RO         3196    i.Delay_ms          delay.o
    0x080005c2   0x080005c2   0x0000002e   Code   RO         3198    i.Delay_us          delay.o
    0x080005f0   0x080005f0   0x0000014c   Code   RO         3864    i.Display_MainPage  main.o
    0x0800073c   0x0800073c   0x00000084   Code   RO         3865    i.Display_SettingsPage  main.o
    0x080007c0   0x080007c0   0x000000fc   Code   RO         3866    i.Display_StatusPage  main.o
    0x080008bc   0x080008bc   0x0000010c   Code   RO         3682    i.EXTI0_IRQHandler  encoder.o
    0x080009c8   0x080009c8   0x0000010c   Code   RO         3683    i.EXTI1_IRQHandler  encoder.o
    0x08000ad4   0x08000ad4   0x0000000c   Code   RO          985    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000ae0   0x08000ae0   0x00000028   Code   RO          989    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08000b08   0x08000b08   0x00000094   Code   RO          990    i.EXTI_Init         stm32f10x_exti.o
    0x08000b9c   0x08000b9c   0x00000018   Code   RO         3684    i.Encoder_Get       encoder.o
    0x08000bb4   0x08000bb4   0x000000b0   Code   RO         3686    i.Encoder_Init      encoder.o
    0x08000c64   0x08000c64   0x00000040   Code   RO         1344    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08000ca4   0x08000ca4   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x08000dba   0x08000dba   0x00000012   Code   RO         1351    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08000dcc   0x08000dcc   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08000dd0   0x08000dd0   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000dd4   0x08000dd4   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x08000dde   0x08000dde   0x00000002   PAD
    0x08000de0   0x08000de0   0x00000084   Code   RO         3867    i.Handle_EncoderInput  main.o
    0x08000e64   0x08000e64   0x000000b0   Code   RO         3868    i.Handle_KeyInput   main.o
    0x08000f14   0x08000f14   0x00000090   Code   RO         3869    i.Handle_WorkMode   main.o
    0x08000fa4   0x08000fa4   0x00000004   Code   RO         3968    i.HardFault_Handler  stm32f10x_it.o
    0x08000fa8   0x08000fa8   0x000000ac   Code   RO         3411    i.Key_GetNum        key.o
    0x08001054   0x08001054   0x0000002c   Code   RO         3412    i.Key_Init          key.o
    0x08001080   0x08001080   0x00000038   Code   RO         3413    i.Key_IsPressed     key.o
    0x080010b8   0x080010b8   0x00000014   Code   RO         3774    i.LightSensor_Get   lightsensor.o
    0x080010cc   0x080010cc   0x0000002c   Code   RO         3775    i.LightSensor_Init  lightsensor.o
    0x080010f8   0x080010f8   0x00000004   Code   RO         3969    i.MemManage_Handler  stm32f10x_it.o
    0x080010fc   0x080010fc   0x00000064   Code   RO         3610    i.Motor_Init        motor.o
    0x08001160   0x08001160   0x00000024   Code   RO         3611    i.Motor_SelfTest    motor.o
    0x08001184   0x08001184   0x00000088   Code   RO         3612    i.Motor_SetSpeed    motor.o
    0x0800120c   0x0800120c   0x00000020   Code   RO         3613    i.Motor_SetTargetSpeed  motor.o
    0x0800122c   0x0800122c   0x00000038   Code   RO         3614    i.Motor_Stop        motor.o
    0x08001264   0x08001264   0x000000a0   Code   RO         3615    i.Motor_Update      motor.o
    0x08001304   0x08001304   0x00000002   Code   RO         3970    i.NMI_Handler       stm32f10x_it.o
    0x08001306   0x08001306   0x00000002   PAD
    0x08001308   0x08001308   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08001378   0x08001378   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x0800138c   0x0800138c   0x0000002a   Code   RO         3450    i.OLED_Clear        oled.o
    0x080013b6   0x080013b6   0x00000002   PAD
    0x080013b8   0x080013b8   0x00000050   Code   RO         3451    i.OLED_I2C_Init     oled.o
    0x08001408   0x08001408   0x00000054   Code   RO         3452    i.OLED_I2C_SendByte  oled.o
    0x0800145c   0x0800145c   0x00000034   Code   RO         3453    i.OLED_I2C_Start    oled.o
    0x08001490   0x08001490   0x00000028   Code   RO         3454    i.OLED_I2C_Stop     oled.o
    0x080014b8   0x080014b8   0x000000ae   Code   RO         3455    i.OLED_Init         oled.o
    0x08001566   0x08001566   0x00000014   Code   RO         3456    i.OLED_Pow          oled.o
    0x0800157a   0x0800157a   0x00000022   Code   RO         3457    i.OLED_SetCursor    oled.o
    0x0800159c   0x0800159c   0x00000074   Code   RO         3459    i.OLED_ShowChar     oled.o
    0x08001610   0x08001610   0x00000054   Code   RO         3460    i.OLED_ShowHexNum   oled.o
    0x08001664   0x08001664   0x00000044   Code   RO         3461    i.OLED_ShowNum      oled.o
    0x080016a8   0x080016a8   0x00000066   Code   RO         3462    i.OLED_ShowSignedNum  oled.o
    0x0800170e   0x0800170e   0x00000028   Code   RO         3463    i.OLED_ShowString   oled.o
    0x08001736   0x08001736   0x00000020   Code   RO         3464    i.OLED_WriteCommand  oled.o
    0x08001756   0x08001756   0x00000020   Code   RO         3465    i.OLED_WriteData    oled.o
    0x08001776   0x08001776   0x00000002   PAD
    0x08001778   0x08001778   0x000000a0   Code   RO         3561    i.PWM_Init          pwm.o
    0x08001818   0x08001818   0x00000024   Code   RO         3562    i.PWM_SetCompare3   pwm.o
    0x0800183c   0x0800183c   0x00000002   Code   RO         3971    i.PendSV_Handler    stm32f10x_it.o
    0x0800183e   0x0800183e   0x00000002   PAD
    0x08001840   0x08001840   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08001860   0x08001860   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08001880   0x08001880   0x00000002   Code   RO         3972    i.SVC_Handler       stm32f10x_it.o
    0x08001882   0x08001882   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x0800188a   0x0800188a   0x00000002   PAD
    0x0800188c   0x0800188c   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x0800196c   0x0800196c   0x00000008   Code   RO         3973    i.SysTick_Handler   stm32f10x_it.o
    0x08001974   0x08001974   0x00000010   Code   RO         3199    i.SysTick_Increment  delay.o
    0x08001984   0x08001984   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x080019e4   0x080019e4   0x0000000c   Code   RO         3201    i.System_Delay_ms   delay.o
    0x080019f0   0x080019f0   0x0000000c   Code   RO         3202    i.System_GetError   delay.o
    0x080019fc   0x080019fc   0x0000000c   Code   RO         3203    i.System_GetTick    delay.o
    0x08001a08   0x08001a08   0x000000b0   Code   RO         3204    i.System_Init       delay.o
    0x08001ab8   0x08001ab8   0x00000040   Code   RO         3870    i.System_Init_All   main.o
    0x08001af8   0x08001af8   0x00000002   Code   RO         3206    i.System_WDT_Feed   delay.o
    0x08001afa   0x08001afa   0x00000002   Code   RO         3207    i.System_WDT_Init   delay.o
    0x08001afc   0x08001afc   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x08001b14   0x08001b14   0x0000000c   Code   RO         2448    i.TIM_InternalClockConfig  stm32f10x_tim.o
    0x08001b20   0x08001b20   0x000000a0   Code   RO         2460    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001bc0   0x08001bc0   0x00000014   Code   RO         2468    i.TIM_OCStructInit  stm32f10x_tim.o
    0x08001bd4   0x08001bd4   0x00000004   Code   RO         2484    i.TIM_SetCompare3   stm32f10x_tim.o
    0x08001bd8   0x08001bd8   0x000000a4   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001c7c   0x08001c7c   0x00000048   Code   RO         3871    i.Update_Display    main.o
    0x08001cc4   0x08001cc4   0x00000004   Code   RO         3974    i.UsageFault_Handler  stm32f10x_it.o
    0x08001cc8   0x08001cc8   0x0000006c   Code   RO         3873    i.main              main.o
    0x08001d34   0x08001d34   0x0000005e   Code   RO         4037    x$fpl$dfix          fz_ws.l(dfix.o)
    0x08001d92   0x08001d92   0x00000002   PAD
    0x08001d94   0x08001d94   0x00000154   Code   RO         4041    x$fpl$dmul          fz_ws.l(dmul.o)
    0x08001ee8   0x08001ee8   0x0000009c   Code   RO         4068    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08001f84   0x08001f84   0x0000000c   Code   RO         4070    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08001f90   0x08001f90   0x00000056   Code   RO         4043    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08001fe6   0x08001fe6   0x00000002   PAD
    0x08001fe8   0x08001fe8   0x000000c4   Code   RO         4045    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080020ac   0x080020ac   0x00000184   Code   RO         4052    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08002230   0x08002230   0x0000003e   Code   RO         4055    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x0800226e   0x0800226e   0x00000002   PAD
    0x08002270   0x08002270   0x00000030   Code   RO         4060    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080022a0   0x080022a0   0x00000026   Code   RO         4059    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x080022c6   0x080022c6   0x00000002   PAD
    0x080022c8   0x080022c8   0x00000102   Code   RO         4065    x$fpl$fmul          fz_ws.l(fmul.o)
    0x080023ca   0x080023ca   0x0000008c   Code   RO         4072    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08002456   0x08002456   0x0000000a   Code   RO         4074    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08002460   0x08002460   0x00000014   Code   RO         4046    x$fpl$frsb          fz_ws.l(faddsub_clz.o)
    0x08002474   0x08002474   0x000000ea   Code   RO         4047    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800255e   0x0800255e   0x00000000   Code   RO         4076    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800255e   0x0800255e   0x000005f0   Data   RO         3466    .constdata          oled.o
    0x08002b4e   0x08002b4e   0x00000002   PAD
    0x08002b50   0x08002b50   0x00000020   Data   RO         4229    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08002b70, Size: 0x000006c8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08002b70   0x00000014   Data   RW           28    .data               system_stm32f10x.o
    0x20000014   0x08002b84   0x0000000d   Data   RW         3208    .data               delay.o
    0x20000021   0x08002b91   0x00000003   PAD
    0x20000024   0x08002b94   0x0000000c   Data   RW         3414    .data               key.o
    0x20000030   0x08002ba0   0x00000004   Data   RW         3564    .data               pwm.o
    0x20000034   0x08002ba4   0x00000008   Data   RW         3616    .data               motor.o
    0x2000003c   0x08002bac   0x0000000f   Data   RW         3689    .data               encoder.o
    0x2000004b   0x08002bbb   0x00000001   PAD
    0x2000004c   0x08002bbc   0x00000019   Data   RW         3874    .data               main.o
    0x20000065   0x08002bd5   0x00000003   PAD
    0x20000068        -       0x00000060   Zero   RW         4103    .bss                c_w.l(libspace.o)
    0x200000c8        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x200002c8        -       0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       112         16          0          0          0       1833   buzzer.o
         0          0          0          0          0       4508   core_cm3.o
       302         58          0         13          0     256114   delay.o
       678         38          0          0          0       5468   ds18b20.o
       736         80          0         15          0       3343   encoder.o
       272         22          0         12          0       1732   key.o
        64         10          0          0          0        945   lightsensor.o
      1412        312          0         25          0       5587   main.o
       132         22          0          0          0     204703   misc.o
       520         76          0          8          0       3238   motor.o
      1000         22       1520          0          0       8828   oled.o
       196         12          0          4          0       1341   pwm.o
        64         26        236          0       1536        808   startup_stm32f10x_md.o
       200         18          0          0          0       4044   stm32f10x_exti.o
       378          4          0          0          0      12838   stm32f10x_gpio.o
        32          0          0          0          0       3762   stm32f10x_it.o
        64         12          0          0          0       1050   stm32f10x_rcc.o
       384         52          0          0          0      24006   stm32f10x_tim.o
       328         28          0         20          0      10389   system_stm32f10x.o

    ----------------------------------------------------------------------
      6890        <USER>       <GROUP>        104       1536     554537   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          2          7          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        72          0          0          0          0         80   strcpy.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        94          4          0          0          0         92   dfix.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       450          8          0          0          0        236   faddsub_clz.o
       388         76          0          0          0         96   fdiv.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      2440        <USER>          <GROUP>          0         96       1892   Library Totals
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       344         16          0          0         96        664   c_w.l
      2082        120          0          0          0       1228   fz_ws.l

    ----------------------------------------------------------------------
      2440        <USER>          <GROUP>          0         96       1892   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9330        944       1790        104       1632     550757   Grand Totals
      9330        944       1790        104       1632     550757   ELF Image Totals
      9330        944       1790        104          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                11120 (  10.86kB)
    Total RW  Size (RW Data + ZI Data)              1736 (   1.70kB)
    Total ROM Size (Code + RO Data + RW Data)      11224 (  10.96kB)

==============================================================================

