Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    delay.o(i.Delay_ms) refers to delay.o(i.Delay_us) for Delay_us
    delay.o(i.Delay_s) refers to delay.o(i.Delay_ms) for Delay_ms
    led.o(i.LED0_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED0_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED0_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED1_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED1_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    key.o(i.Key_GetNum) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Key_GetNum) refers to key.o(.data) for key_state
    key.o(i.Key_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.Key_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.Key_IsPressed) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_I2C_Init) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Start) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_I2C_Stop) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_I2C_Init) for OLED_I2C_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    pwm.o(i.PWM_Disable) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_Enable) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_GetCompare3) refers to stm32f10x_tim.o(i.TIM_GetCapture3) for TIM_GetCapture3
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_Init) refers to pwm.o(.data) for pwm_period
    pwm.o(i.PWM_SetCompare3) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    pwm.o(i.PWM_SetFrequency) refers to stm32f10x_tim.o(i.TIM_PrescalerConfig) for TIM_PrescalerConfig
    pwm.o(i.PWM_SetFrequency) refers to pwm.o(.data) for pwm_prescaler
    motor.o(i.Motor_Brake) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_Brake) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_Disable) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_Disable) refers to pwm.o(i.PWM_Disable) for PWM_Disable
    motor.o(i.Motor_Disable) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_Enable) refers to pwm.o(i.PWM_Enable) for PWM_Enable
    motor.o(i.Motor_Enable) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Motor_Init) refers to pwm.o(i.PWM_Init) for PWM_Init
    motor.o(i.Motor_Init) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_Init) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_SelfTest) refers to motor.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor.o(i.Motor_SelfTest) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_SetSpeed) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_SetSpeed) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_SetSpeed) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_SetSpeed) refers to motor.o(i.Motor_Stop) for Motor_Stop
    motor.o(i.Motor_SetSpeed) refers to motor.o(.data) for motor_enabled
    motor.o(i.Motor_Stop) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_Stop) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_Update) refers to motor.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor.o(i.Motor_Update) refers to motor.o(.data) for last_update_tick
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI0_IRQHandler) refers to encoder.o(.data) for last_interrupt_time
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(i.EXTI1_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.EXTI1_IRQHandler) refers to encoder.o(.data) for last_interrupt_time
    encoder.o(i.Encoder_Get) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_GetCount) refers to encoder.o(.data) for Encoder_Total
    encoder.o(i.Encoder_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    encoder.o(i.Encoder_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(i.Encoder_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    encoder.o(i.Encoder_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    encoder.o(i.Encoder_Init) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_Reset) refers to encoder.o(.data) for Encoder_Count
    encoder.o(i.Encoder_SelfTest) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(i.Encoder_SelfTest) refers to encoder.o(.data) for encoder_error_count
    buzzer.o(i.Buzzer_Beep) refers to buzzer.o(i.Buzzer_ON) for Buzzer_ON
    buzzer.o(i.Buzzer_Beep) refers to buzzer.o(i.Buzzer_OFF) for Buzzer_OFF
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    buzzer.o(i.Buzzer_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_OFF) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_ON) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    buzzer.o(i.Buzzer_Turn) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    lightsensor.o(i.LightSensor_Get) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    lightsensor.o(i.LightSensor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lightsensor.o(i.LightSensor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Check) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Check) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Check) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ds18b20.o(i.DS18B20_GetTemperature) refers to ds18b20.o(i.DS18B20_Get_Temp) for DS18B20_Get_Temp
    ds18b20.o(i.DS18B20_GetTemperature) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    ds18b20.o(i.DS18B20_GetTemperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Start) for DS18B20_Start
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to ds18b20.o(i.DS18B20_Read_Byte) for DS18B20_Read_Byte
    ds18b20.o(i.DS18B20_Get_Temp) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    ds18b20.o(i.DS18B20_Get_Temp) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ds18b20.o(i.DS18B20_Get_Temp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ds18b20.o(i.DS18B20_Get_Temp) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Init) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Mode) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    ds18b20.o(i.DS18B20_Mode) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    ds18b20.o(i.DS18B20_Read_Bit) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Read_Bit) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Read_Bit) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    ds18b20.o(i.DS18B20_Read_Byte) refers to ds18b20.o(i.DS18B20_Read_Bit) for DS18B20_Read_Bit
    ds18b20.o(i.DS18B20_Rst) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Rst) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Rst) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Rst) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Rst) for DS18B20_Rst
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Check) for DS18B20_Check
    ds18b20.o(i.DS18B20_Start) refers to ds18b20.o(i.DS18B20_Write_Byte) for DS18B20_Write_Byte
    ds18b20.o(i.DS18B20_Write_Byte) refers to ds18b20.o(i.DS18B20_Mode) for DS18B20_Mode
    ds18b20.o(i.DS18B20_Write_Byte) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    ds18b20.o(i.DS18B20_Write_Byte) refers to delay.o(i.Delay_us) for Delay_us
    ds18b20.o(i.DS18B20_Write_Byte) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    main.o(i.Display_MainPage) refers to strcpy.o(.text) for strcpy
    main.o(i.Display_MainPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_MainPage) refers to oled.o(i.OLED_ShowSignedNum) for OLED_ShowSignedNum
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowHexNum) for OLED_ShowHexNum
    main.o(i.Display_SettingsPage) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Display_StatusPage) refers to ds18b20.o(i.DS18B20_GetTemperature) for DS18B20_GetTemperature
    main.o(i.Display_StatusPage) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.Display_StatusPage) refers to lightsensor.o(i.LightSensor_Get) for LightSensor_Get
    main.o(i.Display_StatusPage) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.Display_StatusPage) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    main.o(i.Display_StatusPage) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.Display_StatusPage) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    main.o(i.Display_StatusPage) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    main.o(i.Display_StatusPage) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.Handle_EncoderInput) refers to encoder.o(i.Encoder_Get) for Encoder_Get
    main.o(i.Handle_EncoderInput) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_KeyInput) refers to key.o(i.Key_GetNum) for Key_GetNum
    main.o(i.Handle_KeyInput) refers to buzzer.o(i.Buzzer_Beep) for Buzzer_Beep
    main.o(i.Handle_KeyInput) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_KeyInput) refers to key.o(i.Key_IsPressed) for Key_IsPressed
    main.o(i.Handle_KeyInput) refers to motor.o(i.Motor_Stop) for Motor_Stop
    main.o(i.Handle_KeyInput) refers to main.o(.data) for KeyNum
    main.o(i.Handle_WorkMode) refers to motor.o(i.Motor_SetTargetSpeed) for Motor_SetTargetSpeed
    main.o(i.Handle_WorkMode) refers to motor.o(i.Motor_SelfTest) for Motor_SelfTest
    main.o(i.Handle_WorkMode) refers to main.o(.data) for mode_timer
    main.o(i.System_Init_All) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.System_Init_All) refers to motor.o(i.Motor_Init) for Motor_Init
    main.o(i.System_Init_All) refers to key.o(i.Key_Init) for Key_Init
    main.o(i.System_Init_All) refers to encoder.o(i.Encoder_Init) for Encoder_Init
    main.o(i.System_Init_All) refers to lightsensor.o(i.LightSensor_Init) for LightSensor_Init
    main.o(i.System_Init_All) refers to buzzer.o(i.Buzzer_Init) for Buzzer_Init
    main.o(i.System_Init_All) refers to ds18b20.o(i.DS18B20_Init) for DS18B20_Init
    main.o(i.System_Init_All) refers to main.o(.data) for last_display_update
    main.o(i.Update_Display) refers to main.o(i.Display_MainPage) for Display_MainPage
    main.o(i.Update_Display) refers to main.o(i.Display_StatusPage) for Display_StatusPage
    main.o(i.Update_Display) refers to main.o(i.Display_SettingsPage) for Display_SettingsPage
    main.o(i.Update_Display) refers to main.o(.data) for last_display_update
    main.o(i.Update_LED_Status) refers to led.o(i.LED0_OFF) for LED0_OFF
    main.o(i.Update_LED_Status) refers to led.o(i.LED0_Turn) for LED0_Turn
    main.o(i.Update_LED_Status) refers to led.o(i.LED1_Turn) for LED1_Turn
    main.o(i.Update_LED_Status) refers to main.o(.data) for last_led_update
    main.o(i.main) refers to main.o(i.System_Init_All) for System_Init_All
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to buzzer.o(i.Buzzer_Beep) for Buzzer_Beep
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to main.o(i.Handle_KeyInput) for Handle_KeyInput
    main.o(i.main) refers to main.o(i.Handle_EncoderInput) for Handle_EncoderInput
    main.o(i.main) refers to main.o(i.Handle_WorkMode) for Handle_WorkMode
    main.o(i.main) refers to motor.o(i.Motor_Update) for Motor_Update
    main.o(i.main) refers to main.o(i.Update_Display) for Update_Display
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (40 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetClocksFreq), (212 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rcc.o(.data), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetITStatus), (34 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_Cmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_GetITStatus), (84 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ITConfig), (74 bytes).
    Removing stm32f10x_usart.o(i.USART_Init), (216 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiveData), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing delay.o(i.Delay_ms), (24 bytes).
    Removing delay.o(i.Delay_s), (24 bytes).
    Removing led.o(i.LED0_OFF), (16 bytes).
    Removing led.o(i.LED0_ON), (16 bytes).
    Removing led.o(i.LED0_Turn), (36 bytes).
    Removing led.o(i.LED1_OFF), (16 bytes).
    Removing led.o(i.LED1_ON), (16 bytes).
    Removing led.o(i.LED1_Turn), (36 bytes).
    Removing led.o(i.LED_Init), (48 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (62 bytes).
    Removing pwm.o(i.PWM_Disable), (14 bytes).
    Removing pwm.o(i.PWM_Enable), (12 bytes).
    Removing pwm.o(i.PWM_GetCompare3), (12 bytes).
    Removing pwm.o(i.PWM_SetFrequency), (48 bytes).
    Removing motor.o(i.Motor_Brake), (60 bytes).
    Removing motor.o(i.Motor_Disable), (24 bytes).
    Removing motor.o(i.Motor_Enable), (20 bytes).
    Removing motor.o(i.Motor_GetSpeed), (12 bytes).
    Removing motor.o(i.Motor_GetState), (12 bytes).
    Removing encoder.o(i.Encoder_GetCount), (12 bytes).
    Removing encoder.o(i.Encoder_Reset), (32 bytes).
    Removing encoder.o(i.Encoder_SelfTest), (60 bytes).
    Removing buzzer.o(i.Buzzer_Turn), (44 bytes).
    Removing main.o(i.Update_LED_Status), (100 bytes).

482 unused section(s) (total 19030 bytes) removed from the image.
