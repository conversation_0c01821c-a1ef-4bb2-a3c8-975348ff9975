#include "stm32f10x.h"
#include "Motor.h"
#include "PWM.h"
#include "Delay.h"

static uint8_t motor_enabled = 1;               // 电机使能标志
static uint32_t last_update_tick = 0;           // 上次更新时间

// PID控制器
static PID_t motor_pid;                          // 电机PID控制器
static uint8_t feedback_control_enabled = 0;    // 反馈控制使能标志
static float target_rpm = 0.0f;                 // 目标转速(RPM)

/**
 * 函数：直流电机初始化
 * 参数：无
 * 返回值：无
 */
void Motor_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Pin = MOTOR_DIR1_PIN | MOTOR_DIR2_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(MOTOR_DIR1_PORT, &GPIO_InitStructure);

    // 初始化PWM
    PWM_Init();

    // 初始化PID控制器
    Motor_PID_Init();

    // 初始化电机状态
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
    motor_enabled = 1;
    feedback_control_enabled = ENCODER_FEEDBACK_ENABLE;
    target_rpm = 0.0f;
    last_update_tick = System_GetTick();

    // 设置初始方向为停止
    Motor_Stop();
}

/**
 * 函数：直流电机设置速度
 * 参数：Speed - 要设置的速度，范围：-100~100
 * 返回值：无
 */
void Motor_SetSpeed(int8_t Speed)
{
    if(!motor_enabled) return;

    // 限制速度范围
    Speed = CONSTRAIN(Speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
    g_motor_speed = Speed;

    if (Speed > 0) {
        // 正转
        GPIO_SetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
        GPIO_ResetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
        PWM_SetCompare3(Speed);
        g_motor_state = MOTOR_FORWARD;
    }
    else if (Speed < 0) {
        // 反转
        GPIO_ResetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
        GPIO_SetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
        PWM_SetCompare3(-Speed);
        g_motor_state = MOTOR_BACKWARD;
    }
    else {
        // 停止
        Motor_Stop();
    }
}

/**
 * 函数：设置目标速度(平滑控制)
 * 参数：Speed - 目标速度
 * 返回值：无
 */
void Motor_SetTargetSpeed(int8_t Speed)
{
    g_target_speed = CONSTRAIN(Speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
}

/**
 * 函数：电机状态更新(需定时调用)
 * 参数：无
 * 返回值：无
 */
void Motor_Update(void)
{
    uint32_t current_tick = System_GetTick();

    // 检查更新间隔
    if((current_tick - last_update_tick) < MOTOR_ACCEL_TIME) return;
    last_update_tick = current_tick;

    if(feedback_control_enabled && g_feedback_enabled) {
        // PID反馈控制模式
        float actual_speed = g_actual_speed;  // 实际速度(RPM)
        float pid_output = Motor_PID_Calculate(target_rpm, actual_speed);

        // 将PID输出转换为PWM值
        int8_t pwm_value = (int8_t)pid_output;
        Motor_SetSpeed(pwm_value);

        // 更新全局速度变量用于显示
        g_motor_speed = pwm_value;
    }
    else {
        // 传统开环控制模式
        if(g_motor_speed != g_target_speed) {
            if(g_motor_speed < g_target_speed) {
                g_motor_speed += MOTOR_SPEED_STEP;
                if(g_motor_speed > g_target_speed) g_motor_speed = g_target_speed;
            }
            else {
                g_motor_speed -= MOTOR_SPEED_STEP;
                if(g_motor_speed < g_target_speed) g_motor_speed = g_target_speed;
            }
            Motor_SetSpeed(g_motor_speed);
        }
    }
}

/**
 * 函数：电机停止
 * 参数：无
 * 返回值：无
 */
void Motor_Stop(void)
{
    GPIO_ResetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
    GPIO_ResetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
    PWM_SetCompare3(0);
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
}

/**
 * 函数：电机刹车
 * 参数：无
 * 返回值：无
 */
void Motor_Brake(void)
{
    GPIO_SetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
    GPIO_SetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
    PWM_SetCompare3(MOTOR_SPEED_MAX);
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_BRAKE;
}

/**
 * 函数：获取当前速度
 * 参数：无
 * 返回值：当前速度
 */
int8_t Motor_GetSpeed(void)
{
    return g_motor_speed;
}

/**
 * 函数：获取电机状态
 * 参数：无
 * 返回值：电机状态
 */
MotorState_t Motor_GetState(void)
{
    return g_motor_state;
}

/**
 * 函数：使能电机
 * 参数：无
 * 返回值：无
 */
void Motor_Enable(void)
{
    motor_enabled = 1;
    PWM_Enable();
}

/**
 * 函数：禁用电机
 * 参数：无
 * 返回值：无
 */
void Motor_Disable(void)
{
    Motor_Stop();
    motor_enabled = 0;
    PWM_Disable();
}

/**
 * 函数：电机自检
 * 参数：无
 * 返回值：0-正常, 非0-故障
 */
uint8_t Motor_SelfTest(void)
{
    // 简单的自检程序
    Motor_SetSpeed(20);
    System_Delay_ms(100);
    Motor_SetSpeed(-20);
    System_Delay_ms(100);
    Motor_Stop();

    return 0; // 假设自检通过
}

/**
 * 函数：PID控制器初始化
 * 参数：无
 * 返回值：无
 */
void Motor_PID_Init(void)
{
    motor_pid.kp = PID_KP;
    motor_pid.ki = PID_KI;
    motor_pid.kd = PID_KD;
    motor_pid.setpoint = 0.0f;
    motor_pid.integral = 0.0f;
    motor_pid.prev_error = 0.0f;
    motor_pid.output = 0.0f;
    motor_pid.output_max = PID_OUTPUT_MAX;
    motor_pid.output_min = PID_OUTPUT_MIN;
    motor_pid.integral_max = PID_INTEGRAL_MAX;
}

/**
 * 函数：设置PID参数
 * 参数：kp, ki, kd - PID参数
 * 返回值：无
 */
void Motor_PID_SetParams(float kp, float ki, float kd)
{
    motor_pid.kp = kp;
    motor_pid.ki = ki;
    motor_pid.kd = kd;
}

/**
 * 函数：PID计算
 * 参数：setpoint - 设定值, feedback - 反馈值
 * 返回值：PID输出
 */
float Motor_PID_Calculate(float setpoint, float feedback)
{
    // 计算误差
    float error = setpoint - feedback;

    // 积分项
    motor_pid.integral += error;

    // 积分限幅
    if(motor_pid.integral > motor_pid.integral_max) {
        motor_pid.integral = motor_pid.integral_max;
    }
    else if(motor_pid.integral < -motor_pid.integral_max) {
        motor_pid.integral = -motor_pid.integral_max;
    }

    // 微分项
    float derivative = error - motor_pid.prev_error;
    motor_pid.prev_error = error;

    // PID输出计算
    motor_pid.output = motor_pid.kp * error +
                       motor_pid.ki * motor_pid.integral +
                       motor_pid.kd * derivative;

    // 输出限幅
    if(motor_pid.output > motor_pid.output_max) {
        motor_pid.output = motor_pid.output_max;
    }
    else if(motor_pid.output < motor_pid.output_min) {
        motor_pid.output = motor_pid.output_min;
    }

    return motor_pid.output;
}

/**
 * 函数：使能反馈控制
 * 参数：enable - 1使能, 0禁用
 * 返回值：无
 */
void Motor_EnableFeedbackControl(uint8_t enable)
{
    feedback_control_enabled = enable;
    if(enable) {
        // 重置PID控制器
        motor_pid.integral = 0.0f;
        motor_pid.prev_error = 0.0f;
        motor_pid.output = 0.0f;
    }
}

/**
 * 函数：设置速度(RPM)
 * 参数：rpm - 目标转速
 * 返回值：无
 */
void Motor_SetSpeedRPM(float rpm)
{
    target_rpm = rpm;
    motor_pid.setpoint = rpm;

    // 如果未使能反馈控制，转换为传统PWM值
    if(!feedback_control_enabled || !g_feedback_enabled) {
        // 简单的RPM到PWM转换 (假设100RPM对应100%PWM)
        int8_t pwm_value = (int8_t)(rpm * 100.0f / 100.0f);
        Motor_SetTargetSpeed(pwm_value);
    }
}

/**
 * 函数：获取实际速度(RPM)
 * 参数：无
 * 返回值：实际转速
 */
float Motor_GetActualSpeedRPM(void)
{
    return g_actual_speed;
}
