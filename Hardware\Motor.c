#include "stm32f10x.h"
#include "Motor.h"
#include "PWM.h"
#include "System.h"

static uint8_t motor_enabled = 1;               // 电机使能标志
static uint32_t last_update_tick = 0;           // 上次更新时间

/**
 * 函数：直流电机初始化
 * 参数：无
 * 返回值：无
 */
void Motor_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Pin = MOTOR_DIR1_PIN | MOTOR_DIR2_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(MOTOR_DIR1_PORT, &GPIO_InitStructure);

    // 初始化PWM
    PWM_Init();

    // 初始化电机状态
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
    motor_enabled = 1;
    last_update_tick = System_GetTick();

    // 设置初始方向为停止
    Motor_Stop();
}

/**
 * 函数：直流电机设置速度
 * 参数：Speed - 要设置的速度，范围：-100~100
 * 返回值：无
 */
void Motor_SetSpeed(int8_t Speed)
{
    if(!motor_enabled) return;

    // 限制速度范围
    Speed = CONSTRAIN(Speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
    g_motor_speed = Speed;

    if (Speed > 0) {
        // 正转
        GPIO_SetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
        GPIO_ResetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
        PWM_SetCompare3(Speed);
        g_motor_state = MOTOR_FORWARD;
    }
    else if (Speed < 0) {
        // 反转
        GPIO_ResetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
        GPIO_SetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
        PWM_SetCompare3(-Speed);
        g_motor_state = MOTOR_BACKWARD;
    }
    else {
        // 停止
        Motor_Stop();
    }
}

/**
 * 函数：设置目标速度(平滑控制)
 * 参数：Speed - 目标速度
 * 返回值：无
 */
void Motor_SetTargetSpeed(int8_t Speed)
{
    g_target_speed = CONSTRAIN(Speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
}

/**
 * 函数：电机状态更新(需定时调用)
 * 参数：无
 * 返回值：无
 */
void Motor_Update(void)
{
    uint32_t current_tick = System_GetTick();

    // 检查更新间隔
    if((current_tick - last_update_tick) < MOTOR_ACCEL_TIME) return;
    last_update_tick = current_tick;

    // 平滑速度控制
    if(g_motor_speed != g_target_speed) {
        if(g_motor_speed < g_target_speed) {
            g_motor_speed += MOTOR_SPEED_STEP;
            if(g_motor_speed > g_target_speed) g_motor_speed = g_target_speed;
        }
        else {
            g_motor_speed -= MOTOR_SPEED_STEP;
            if(g_motor_speed < g_target_speed) g_motor_speed = g_target_speed;
        }
        Motor_SetSpeed(g_motor_speed);
    }
}

/**
 * 函数：电机停止
 * 参数：无
 * 返回值：无
 */
void Motor_Stop(void)
{
    GPIO_ResetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
    GPIO_ResetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
    PWM_SetCompare3(0);
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
}

/**
 * 函数：电机刹车
 * 参数：无
 * 返回值：无
 */
void Motor_Brake(void)
{
    GPIO_SetBits(MOTOR_DIR1_PORT, MOTOR_DIR1_PIN);
    GPIO_SetBits(MOTOR_DIR2_PORT, MOTOR_DIR2_PIN);
    PWM_SetCompare3(MOTOR_SPEED_MAX);
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_BRAKE;
}

/**
 * 函数：获取当前速度
 * 参数：无
 * 返回值：当前速度
 */
int8_t Motor_GetSpeed(void)
{
    return g_motor_speed;
}

/**
 * 函数：获取电机状态
 * 参数：无
 * 返回值：电机状态
 */
MotorState_t Motor_GetState(void)
{
    return g_motor_state;
}

/**
 * 函数：使能电机
 * 参数：无
 * 返回值：无
 */
void Motor_Enable(void)
{
    motor_enabled = 1;
    PWM_Enable();
}

/**
 * 函数：禁用电机
 * 参数：无
 * 返回值：无
 */
void Motor_Disable(void)
{
    Motor_Stop();
    motor_enabled = 0;
    PWM_Disable();
}

/**
 * 函数：电机自检
 * 参数：无
 * 返回值：0-正常, 非0-故障
 */
uint8_t Motor_SelfTest(void)
{
    // 简单的自检程序
    Motor_SetSpeed(20);
    System_Delay_ms(100);
    Motor_SetSpeed(-20);
    System_Delay_ms(100);
    Motor_Stop();

    return 0; // 假设自检通过
}
