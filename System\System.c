#include "System.h"
#include "Delay.h"

//=============================================================================
// 全局变量定义
//=============================================================================
volatile int8_t g_motor_speed = 0;          // 当前电机速度
volatile int8_t g_target_speed = 0;         // 目标速度
volatile MotorState_t g_motor_state = MOTOR_STOP; // 电机状态
volatile WorkMode_t g_work_mode = MODE_MANUAL;     // 工作模式
volatile uint8_t g_system_error = ERROR_NONE;     // 系统错误标志
volatile uint32_t g_system_tick = 0;              // 系统滴答计数

static SystemStatus_t system_status = SYSTEM_OK;  // 系统状态

/**
 * 函数：系统初始化
 * 参数：无
 * 返回值：无
 */
void System_Init(void)
{
    System_Clock_Init();    // 时钟初始化
    System_NVIC_Init();     // 中断优先级分组
    System_SysTick_Init();  // SysTick初始化
    
    // 初始化全局变量
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
    g_work_mode = MODE_MANUAL;
    g_system_error = ERROR_NONE;
    g_system_tick = 0;
    
    system_status = SYSTEM_OK;
}

/**
 * 函数：系统时钟初始化
 * 参数：无
 * 返回值：无
 */
void System_Clock_Init(void)
{
    // 使用外部8MHz晶振，PLL倍频到72MHz
    RCC_DeInit();
    RCC_HSEConfig(RCC_HSE_ON);
    
    if(RCC_WaitForHSEStartUp() == SUCCESS) {
        RCC_HCLKConfig(RCC_SYSCLK_Div1);    // AHB = 72MHz
        RCC_PCLK2Config(RCC_HCLK_Div1);     // APB2 = 72MHz
        RCC_PCLK1Config(RCC_HCLK_Div2);     // APB1 = 36MHz
        
        RCC_PLLConfig(RCC_PLLSource_HSE_Div1, RCC_PLLMul_9); // PLL = 8MHz * 9 = 72MHz
        RCC_PLLCmd(ENABLE);
        
        while(RCC_GetFlagStatus(RCC_FLAG_PLLRDY) == RESET);
        
        RCC_SYSCLKConfig(RCC_SYSCLKSource_PLLCLK);
        while(RCC_GetSYSCLKSource() != 0x08);
    }
}

/**
 * 函数：中断优先级分组初始化
 * 参数：无
 * 返回值：无
 */
void System_NVIC_Init(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 抢占优先级0-3，响应优先级0-3
}

/**
 * 函数：SysTick初始化
 * 参数：无
 * 返回值：无
 */
void System_SysTick_Init(void)
{
    SysTick_Config(SYSTEM_CLOCK_FREQ / SYSTICK_FREQ); // 1ms中断一次
}

/**
 * 函数：SysTick中断处理函数
 * 参数：无
 * 返回值：无
 */
void SysTick_Handler(void)
{
    g_system_tick++;
}

/**
 * 函数：获取系统状态
 * 参数：无
 * 返回值：系统状态
 */
SystemStatus_t System_GetStatus(void)
{
    return system_status;
}

/**
 * 函数：设置错误状态
 * 参数：error_code - 错误码
 * 返回值：无
 */
void System_SetError(uint8_t error_code)
{
    g_system_error |= error_code;
    if(g_system_error != ERROR_NONE) {
        system_status = SYSTEM_ERROR;
    }
}

/**
 * 函数：清除错误状态
 * 参数：error_code - 错误码
 * 返回值：无
 */
void System_ClearError(uint8_t error_code)
{
    g_system_error &= ~error_code;
    if(g_system_error == ERROR_NONE) {
        system_status = SYSTEM_OK;
    }
}

/**
 * 函数：获取错误状态
 * 参数：无
 * 返回值：错误码
 */
uint8_t System_GetError(void)
{
    return g_system_error;
}

/**
 * 函数：系统复位
 * 参数：无
 * 返回值：无
 */
void System_Reset(void)
{
    NVIC_SystemReset();
}

/**
 * 函数：获取系统滴答计数
 * 参数：无
 * 返回值：滴答计数
 */
uint32_t System_GetTick(void)
{
    return g_system_tick;
}

/**
 * 函数：毫秒延时
 * 参数：ms - 延时时间(毫秒)
 * 返回值：无
 */
void System_Delay_ms(uint32_t ms)
{
    uint32_t start_tick = g_system_tick;
    while((g_system_tick - start_tick) < ms);
}

/**
 * 函数：超时检查
 * 参数：start_tick - 开始时间, timeout_ms - 超时时间
 * 返回值：1-超时, 0-未超时
 */
uint8_t System_CheckTimeout(uint32_t start_tick, uint32_t timeout_ms)
{
    return ((g_system_tick - start_tick) >= timeout_ms) ? 1 : 0;
}

/**
 * 函数：看门狗初始化
 * 参数：无
 * 返回值：无
 */
void System_WDT_Init(void)
{
    IWDG_WriteAccessCmd(IWDG_WriteAccess_Enable);
    IWDG_SetPrescaler(IWDG_Prescaler_64);    // 分频64
    IWDG_SetReload(625);                     // 重载值625，约1秒超时
    IWDG_ReloadCounter();
    IWDG_Enable();
}

/**
 * 函数：喂狗
 * 参数：无
 * 返回值：无
 */
void System_WDT_Feed(void)
{
    IWDG_ReloadCounter();
}

/**
 * 函数：进入睡眠模式
 * 参数：无
 * 返回值：无
 */
void System_EnterSleep(void)
{
    __WFI(); // 等待中断
}

/**
 * 函数：进入停止模式
 * 参数：无
 * 返回值：无
 */
void System_EnterStop(void)
{
    PWR_EnterSTOPMode(PWR_Regulator_LowPower, PWR_STOPEntry_WFI);
}

/**
 * 函数：唤醒系统
 * 参数：无
 * 返回值：无
 */
void System_Wakeup(void)
{
    System_Clock_Init(); // 重新初始化时钟
}
