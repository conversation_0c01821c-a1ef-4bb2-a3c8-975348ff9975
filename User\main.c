#include "stm32f10x.h"
#include "Config.h"
#include "Delay.h"
#include "OLED.h"
#include "Motor.h"
#include "Key.h"
#include "Encoder.h"
#include "LED.h"
#include "LightSensor.h"
#include "Buzzer.h"
#include "PWM.h"
#include "ds18b20.h"
#include "string.h"

// 函数声明
float DS18B20_GetTemperature(void);

// 全局变量
static uint8_t KeyNum = 0;                  // 按键键码
static uint32_t last_display_update = 0;    // 上次显示更新时间
static uint32_t last_led_update = 0;        // 上次LED更新时间
static uint8_t display_page = 0;            // 显示页面
static uint8_t auto_mode_direction = 1;     // 自动模式方向

// 函数声明
void System_Init_All(void);
void Handle_KeyInput(void);
void Handle_EncoderInput(void);
void Update_Display(void);
void Update_LED_Status(void);
void Handle_WorkMode(void);
void Display_MainPage(void);
void Display_StatusPage(void);
void Display_SettingsPage(void);

int main(void)
{
    // 系统初始化
    System_Init_All();

    // 启动提示
    OLED_ShowString(1, 1, "Motor Control");
    OLED_ShowString(2, 1, "System Ready");
    Buzzer_Beep(100);
    System_Delay_ms(1000);
    OLED_Clear();

    while (1)
    {
        // 处理按键输入
        Handle_KeyInput();

        // 处理编码器输入
        Handle_EncoderInput();

        // 处理工作模式
        Handle_WorkMode();

        // 更新编码器速度计算
        Encoder_UpdateSpeed();

        // 更新电机状态
        Motor_Update();

        // 更新显示
        Update_Display();

//        // 更新LED状态
//        Update_LED_Status();

        // 喂狗
        System_WDT_Feed();

        // 短暂延时
        System_Delay_ms(10);
    }
}

/**
 * 函数：系统初始化
 * 参数：无
 * 返回值：无
 */
void System_Init_All(void)
{
    // 系统基础初始化
    System_Init();

    // 硬件模块初始化
    OLED_Init();
    Motor_Init();
    Key_Init();
    Encoder_Init();
//    LED_Init();
    LightSensor_Init();
    Buzzer_Init();
    DS18B20_Init();

    // 看门狗初始化
    System_WDT_Init();

    // 初始化时间戳
    last_display_update = System_GetTick();
    last_led_update = System_GetTick();
}

/**
 * 函数：处理按键输入
 * 参数：无
 * 返回值：无
 */
void Handle_KeyInput(void)
{
    KeyNum = Key_GetNum();

    if (KeyNum == 1) {
        // 按键1：切换工作模式
        static uint32_t last_key1_time = 0;
        uint32_t current_time = System_GetTick();

        // 检查是否为双击(500ms内)
        if((current_time - last_key1_time) < 500) {
            // 双击：切换反馈控制模式
            g_feedback_enabled = !g_feedback_enabled;
            Motor_EnableFeedbackControl(g_feedback_enabled);
            Encoder_EnableFeedback(g_feedback_enabled);
            Buzzer_Beep(g_feedback_enabled ? 100 : 50);
        }
        else {
            // 单击：切换工作模式
            g_work_mode = (g_work_mode + 1) % 4;
            Buzzer_Beep(50);

            // 模式切换时停止电机
            if(g_work_mode != MODE_MANUAL) {
                Motor_SetTargetSpeed(0);
                Motor_SetSpeedRPM(0);
            }
        }
        last_key1_time = current_time;
    }
    else if (KeyNum == 2) {
        // 按键2：切换显示页面或紧急停止
        if(Key_IsPressed(2)) {
            // 长按紧急停止
            static uint32_t press_start = 0;
            if(press_start == 0) {
                press_start = System_GetTick();
            }
            else if((System_GetTick() - press_start) > 1000) {
                Motor_Stop();
                g_work_mode = MODE_MANUAL;
                Buzzer_Beep(200);
                press_start = 0;
            }
        }
        else {
            // 短按切换页面
            display_page = (display_page + 1) % 3;
            Buzzer_Beep(30);
        }
    }
}

/**
 * 函数：处理编码器输入
 * 参数：无
 * 返回值：无
 */
void Handle_EncoderInput(void)
{
    int16_t encoder_delta = Encoder_Get();

    if(encoder_delta != 0) {
        if(g_work_mode == MODE_MANUAL) {
            if(g_feedback_enabled) {
                // 反馈控制模式：调节RPM
                static float target_rpm = 0.0f;
                target_rpm += encoder_delta * 5.0f;  // 每步5RPM
                target_rpm = CONSTRAIN(target_rpm, -500.0f, 500.0f);
                Motor_SetSpeedRPM(target_rpm);
            }
            else {
                // 开环模式：直接调节PWM
                int16_t new_speed = g_target_speed + encoder_delta * MOTOR_SPEED_STEP;
                new_speed = CONSTRAIN(new_speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
                Motor_SetTargetSpeed(new_speed);
            }
        }
        else if(g_work_mode == MODE_SPEED_TEST) {
            // 速度测试模式：调节测试速度
            if(g_feedback_enabled) {
                static float test_rpm = 0.0f;
                test_rpm += encoder_delta * 10.0f;  // 每步10RPM
                test_rpm = CONSTRAIN(test_rpm, -500.0f, 500.0f);
                Motor_SetSpeedRPM(test_rpm);
            }
            else {
                int16_t new_speed = g_target_speed + encoder_delta * 10;
                new_speed = CONSTRAIN(new_speed, MOTOR_SPEED_MIN, MOTOR_SPEED_MAX);
                Motor_SetTargetSpeed(new_speed);
            }
        }
    }
}

/**
 * 函数：处理工作模式
 * 参数：无
 * 返回值：无
 */
void Handle_WorkMode(void)
{
    static uint32_t mode_timer = 0;
    uint32_t current_tick = System_GetTick();

    switch(g_work_mode) {
        case MODE_MANUAL:
            // 手动模式：由编码器和按键控制
            break;

        case MODE_AUTO:
            // 自动模式：自动往返运行
            if((current_tick - mode_timer) > 2000) {
                mode_timer = current_tick;
                if(g_target_speed == 0) {
                    Motor_SetTargetSpeed(50 * auto_mode_direction);
                }
                else {
                    auto_mode_direction = -auto_mode_direction;
                    Motor_SetTargetSpeed(50 * auto_mode_direction);
                }
            }
            break;

        case MODE_SPEED_TEST:
            // 速度测试模式：保持设定速度运行
            break;

        case MODE_CALIBRATION:
            // 校准模式：执行电机校准
            if((current_tick - mode_timer) > 5000) {
                Motor_SelfTest();
                g_work_mode = MODE_MANUAL;
                mode_timer = current_tick;
            }
            break;
    }
}

/**
 * 函数：更新显示
 * 参数：无
 * 返回值：无
 */
void Update_Display(void)
{
    uint32_t current_tick = System_GetTick();

    // 控制显示刷新频率
    if((current_tick - last_display_update) < OLED_REFRESH_PERIOD) return;
    last_display_update = current_tick;

    // 根据页面显示不同内容
    switch(display_page) {
        case 0:
            Display_MainPage();
            break;
        case 1:
            Display_StatusPage();
            break;
        case 2:
            Display_SettingsPage();
            break;
    }
}

/**
 * 函数：显示主页面
 * 参数：无
 * 返回值：无
 */
void Display_MainPage(void)
{
    char mode_str[16];

    // 显示工作模式
    switch(g_work_mode) {
        case MODE_MANUAL:    strcpy(mode_str, "Manual"); break;
        case MODE_AUTO:      strcpy(mode_str, "Auto"); break;
        case MODE_SPEED_TEST: strcpy(mode_str, "Test"); break;
        case MODE_CALIBRATION: strcpy(mode_str, "Calib"); break;
    }

    OLED_ShowString(1, 1, "Mode:");
    OLED_ShowString(1, 6, mode_str);

    // 显示速度信息
    if(g_feedback_enabled) {
        // 反馈模式：显示实际RPM
        OLED_ShowString(2, 1, "RPM:");
        OLED_ShowSignedNum(2, 5, (int32_t)g_actual_speed, 4);

        // 显示目标RPM
        OLED_ShowString(3, 1, "Tgt:");
        OLED_ShowSignedNum(3, 5, (int32_t)Motor_GetActualSpeedRPM(), 4);
    }
    else {
        // 开环模式：显示PWM值
        OLED_ShowString(2, 1, "PWM:");
        OLED_ShowSignedNum(2, 5, g_motor_speed, 3);

        OLED_ShowString(3, 1, "Tgt:");
        OLED_ShowSignedNum(3, 5, g_target_speed, 3);
    }

    // 显示控制模式和电机状态
    OLED_ShowString(4, 1, g_feedback_enabled ? "FB:" : "OL:");
    switch(g_motor_state) {
        case MOTOR_STOP:     OLED_ShowString(4, 4, "STOP"); break;
        case MOTOR_FORWARD:  OLED_ShowString(4, 4, "FWD "); break;
        case MOTOR_BACKWARD: OLED_ShowString(4, 4, "BWD "); break;
        case MOTOR_BRAKE:    OLED_ShowString(4, 4, "BRK "); break;
    }

    // 显示编码器位置
    if(g_feedback_enabled) {
        OLED_ShowString(4, 9, "P:");
        OLED_ShowNum(4, 11, ABS(g_encoder_position) % 1000, 3);
    }
}

/**
 * 函数：显示状态页面
 * 参数：无
 * 返回值：无
 */
void Display_StatusPage(void)
{
    float temperature = DS18B20_GetTemperature();
    uint8_t light_level = LightSensor_Get();

    OLED_ShowString(1, 1, "=== STATUS ===");

    // 显示温度
    OLED_ShowString(2, 1, "Temp:");
    OLED_ShowNum(2, 6, (uint32_t)temperature, 2);
    OLED_ShowString(2, 8, ".");
    OLED_ShowNum(2, 9, (uint32_t)((temperature - (uint32_t)temperature) * 10), 1);
    OLED_ShowString(2, 10, "C");

    // 显示光照
    OLED_ShowString(3, 1, "Light:");
    OLED_ShowNum(3, 7, light_level, 1);

    // 显示系统时间
    OLED_ShowString(4, 1, "Time:");
    OLED_ShowNum(4, 6, System_GetTick() / 1000, 5);
    OLED_ShowString(4, 11, "s");
}

/**
 * 函数：显示设置页面
 * 参数：无
 * 返回值：无
 */
void Display_SettingsPage(void)
{
    OLED_ShowString(1, 1, "== SETTINGS ==");

    // 显示错误状态
    OLED_ShowString(2, 1, "Error:");
    OLED_ShowHexNum(2, 7, System_GetError(), 2);

    // 显示PWM频率
    OLED_ShowString(3, 1, "PWM:");
    OLED_ShowNum(3, 5, MOTOR_PWM_FREQ / 1000, 2);
    OLED_ShowString(3, 7, "kHz");

    // 显示版本信息
    OLED_ShowString(4, 1, "Ver: v1.0");
}

/**
 * 函数：更新LED状态
 * 参数：无
 * 返回值：无
 */
void Update_LED_Status(void)
{
    uint32_t current_tick = System_GetTick();

    // 控制LED更新频率
    if((current_tick - last_led_update) < LED_BLINK_PERIOD) return;
    last_led_update = current_tick;

    // LED0指示电机状态
    if(g_motor_state == MOTOR_STOP) {
        LED0_OFF();
    }
    else {
        LED0_Turn();  // 闪烁表示运行
    }

    // LED1指示系统状态
    if(System_GetError() != ERROR_NONE) {
        LED1_Turn();  // 快速闪烁表示错误
    }
    else {
        static uint8_t heartbeat_count = 0;
        heartbeat_count++;
        if(heartbeat_count >= 10) {  // 慢闪表示正常
            LED1_Turn();
            heartbeat_count = 0;
        }
    }
}
