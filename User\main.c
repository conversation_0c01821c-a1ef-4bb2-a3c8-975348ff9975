#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Motor.h"
#include "Key.h"
#include "Encoder.h"
#include "LED.h"
#include "LightSensor.h"
#include "Buzzer.h"
#include "PWM.h"
#include "ds18b20.h"
#include "string.h"

uint8_t KeyNum;		//定义用于接收按键键码的变量按键1是A11按键2是A12
int8_t Speed;

int main(void)
{
	/*模块初始化*/
	OLED_Init();		//OLED初始化
	Motor_Init();		//直流电机初始化
	Key_Init();			//按键初始化
	Encoder_Init();		//旋转编码器初始化
//	LED_Init();
	LightSensor_Init();
	Buzzer_Init();
	DS18B20_Init();
	/*显示静态字符串*/
	OLED_ShowString(1, 1, "Speed:");		//1行1列显示字符串Speed:
	
	while (1)
	{
//		LED1_ON();
//		Delay_ms(500);
//		LED1_OFF();
//		LED0_ON();
//		Delay_ms(500);
//		LED0_OFF();
		Speed += Encoder_Get();
		if(Speed >=100)
			Speed = 0;
		if(Speed<=-100)
			Speed = 0;
//		KeyNum = Key_GetNum();				//获取按键键码
//		if (KeyNum == 1)					//按键1按下
//		{
//			Speed += 20;					//速度变量自增20
//			if (Speed > 100)				//速度变量超过100后
//			{
//				Speed = -100;				//速度变量变为-100
//											//此操作会让电机旋转方向突然改变，可能会因供电不足而导致单片机复位
//											//若出现了此现象，则应避免使用这样的操作
//			}
//		}
		Motor_SetSpeed(Speed);				//设置直流电机的速度为速度变量
		OLED_ShowSignedNum(1, 7, Speed, 3);	//OLED显示速度变量
	}
}
