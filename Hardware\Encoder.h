#ifndef __ENCODER_H
#define __ENCODER_H

#include "Config.h"

void Encoder_Init(void);                    // 编码器初始化
int16_t Encoder_Get(void);                  // 获取编码器增量值
int16_t Encoder_GetCount(void);             // 获取编码器总计数
void Encoder_Reset(void);                   // 重置编码器计数
uint8_t Encoder_SelfTest(void);             // 编码器自检

// 编码器反馈功能
void Encoder_UpdateSpeed(void);             // 更新速度计算
float Encoder_GetSpeed(void);               // 获取实际速度
int32_t Encoder_GetPosition(void);          // 获取绝对位置
void Encoder_SetPosition(int32_t pos);      // 设置位置
void Encoder_EnableFeedback(uint8_t enable); // 使能/禁用反馈控制

#endif
