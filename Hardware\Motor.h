#ifndef __MOTOR_H
#define __MOTOR_H

#include "Config.h"

void Motor_Init(void);                      // 电机初始化
void Motor_SetSpeed(int8_t Speed);          // 设置电机速度
void Motor_SetTargetSpeed(int8_t Speed);    // 设置目标速度(平滑控制)
void Motor_Update(void);                    // 电机状态更新(需定时调用)
void Motor_Stop(void);                      // 电机停止
void Motor_Brake(void);                     // 电机刹车
int8_t Motor_GetSpeed(void);                // 获取当前速度
MotorState_t Motor_GetState(void);          // 获取电机状态
void Motor_Enable(void);                    // 使能电机
void Motor_Disable(void);                   // 禁用电机
uint8_t Motor_SelfTest(void);               // 电机自检

#endif
