#include "stm32f10x.h"
#include "PWM.h"

static uint16_t pwm_period = MOTOR_PWM_RESOLUTION - 1;  // PWM周期
static uint16_t pwm_prescaler = MOTOR_PWM_PRESCALER - 1; // PWM预分频器

/**
 * 函数：PWM初始化
 * 参数：无
 * 返回值：无
 */
void PWM_Init(void)
{
    // 开启时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Pin = MOTOR_PWM_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(MOTOR_PWM_PORT, &GPIO_InitStructure);

    // 配置时钟源
    TIM_InternalClockConfig(MOTOR_PWM_TIMER);

    // 时基单元初始化
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInitStructure.TIM_Period = pwm_period;
    TIM_TimeBaseInitStructure.TIM_Prescaler = pwm_prescaler;
    TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;
    TIM_TimeBaseInit(MOTOR_PWM_TIMER, &TIM_TimeBaseInitStructure);

    // 输出比较初始化
    TIM_OCInitTypeDef TIM_OCInitStructure;
    TIM_OCStructInit(&TIM_OCInitStructure);
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_Pulse = 0;
    TIM_OC3Init(MOTOR_PWM_TIMER, &TIM_OCInitStructure);

    // 使能TIM
    TIM_Cmd(MOTOR_PWM_TIMER, ENABLE);
}

/**
 * 函数：PWM设置CCR
 * 参数：Compare - 要写入的CCR的值，范围：0~100
 * 返回值：无
 * 注意：占空比 = CCR / (ARR + 1)
 */
void PWM_SetCompare3(uint16_t Compare)
{
    Compare = CONSTRAIN(Compare, 0, MOTOR_PWM_RESOLUTION);
    TIM_SetCompare3(MOTOR_PWM_TIMER, Compare);
}

/**
 * 函数：设置PWM频率
 * 参数：freq - PWM频率(Hz)
 * 返回值：无
 */
void PWM_SetFrequency(uint16_t freq)
{
    if(freq > 0) {
        pwm_prescaler = (SYSTEM_CLOCK_FREQ / (freq * MOTOR_PWM_RESOLUTION)) - 1;
        TIM_PrescalerConfig(MOTOR_PWM_TIMER, pwm_prescaler, TIM_PSCReloadMode_Immediate);
    }
}

/**
 * 函数：获取当前PWM占空比
 * 参数：无
 * 返回值：当前CCR值
 */
uint16_t PWM_GetCompare3(void)
{
    return TIM_GetCapture3(MOTOR_PWM_TIMER);
}

/**
 * 函数：使能PWM输出
 * 参数：无
 * 返回值：无
 */
void PWM_Enable(void)
{
    TIM_Cmd(MOTOR_PWM_TIMER, ENABLE);
}

/**
 * 函数：禁用PWM输出
 * 参数：无
 * 返回值：无
 */
void PWM_Disable(void)
{
    TIM_Cmd(MOTOR_PWM_TIMER, DISABLE);
}
