#include "stm32f10x.h"
#include "Config.h"

//=============================================================================
// 全局变量定义
//=============================================================================
volatile int8_t g_motor_speed = 0;          // 当前电机速度
volatile int8_t g_target_speed = 0;         // 目标速度
volatile MotorState_t g_motor_state = MOTOR_STOP; // 电机状态
volatile WorkMode_t g_work_mode = MODE_MANUAL;     // 工作模式
volatile uint8_t g_system_error = ERROR_NONE;     // 系统错误标志
volatile uint32_t g_system_tick = 0;              // 系统滴答计数

// 编码器反馈相关变量
volatile float g_actual_speed = 0.0f;       // 实际测量速度
volatile int32_t g_encoder_position = 0;    // 编码器位置
volatile uint8_t g_feedback_enabled = ENCODER_FEEDBACK_ENABLE; // 反馈控制使能

static uint8_t system_initialized = 0;

/**
  * @brief  微秒级延时
  * @param  xus 延时时长，范围：0~233015
  * @retval 无
  */
void Delay_us(uint32_t xus)
{
	SysTick->LOAD = 72 * xus;				//设置定时器重装值
	SysTick->VAL = 0x00;					//清空当前计数值
	SysTick->CTRL = 0x00000005;				//设置时钟源为HCLK，启动定时器
	while(!(SysTick->CTRL & 0x00010000));	//等待计数到0
	SysTick->CTRL = 0x00000004;				//关闭定时器
}

/**
  * @brief  毫秒级延时
  * @param  xms 延时时长，范围：0~4294967295
  * @retval 无
  */
void Delay_ms(uint32_t xms)
{
	while(xms--)
	{
		Delay_us(1000);
	}
}
 
/**
  * @brief  秒级延时
  * @param  xs 延时时长，范围：0~4294967295
  * @retval 无
  */
void Delay_s(uint32_t xs)
{
	while(xs--)
	{
		Delay_ms(1000);
	}
}

//=============================================================================
// 系统函数实现
//=============================================================================

/**
 * 函数：系统初始化
 * 参数：无
 * 返回值：无
 */
void System_Init(void)
{
    if(system_initialized) return;

    // 配置中断优先级分组
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);

    // 配置SysTick为1ms中断
    SysTick_Config(SystemCoreClock / 1000);

    // 初始化全局变量
    g_motor_speed = 0;
    g_target_speed = 0;
    g_motor_state = MOTOR_STOP;
    g_work_mode = MODE_MANUAL;
    g_system_error = ERROR_NONE;
    g_system_tick = 0;

    system_initialized = 1;
}

/**
 * 函数：SysTick中断处理函数(在stm32f10x_it.c中实现)
 * 参数：无
 * 返回值：无
 */
void SysTick_Increment(void)
{
    g_system_tick++;
}

/**
 * 函数：获取系统滴答计数
 * 参数：无
 * 返回值：滴答计数
 */
uint32_t System_GetTick(void)
{
    return g_system_tick;
}

/**
 * 函数：系统延时(毫秒)
 * 参数：ms - 延时时间
 * 返回值：无
 */
void System_Delay_ms(uint32_t ms)
{
    Delay_ms(ms);
}

/**
 * 函数：获取系统错误状态
 * 参数：无
 * 返回值：错误码
 */
uint8_t System_GetError(void)
{
    return g_system_error;
}

/**
 * 函数：设置系统错误
 * 参数：error_code - 错误码
 * 返回值：无
 */
void System_SetError(uint8_t error_code)
{
    g_system_error |= error_code;
}

/**
 * 函数：清除系统错误
 * 参数：error_code - 错误码
 * 返回值：无
 */
void System_ClearError(uint8_t error_code)
{
    g_system_error &= ~error_code;
}

/**
 * 函数：看门狗初始化(空实现)
 * 参数：无
 * 返回值：无
 */
void System_WDT_Init(void)
{
    // 简化实现，不启用看门狗
}

/**
 * 函数：喂狗(空实现)
 * 参数：无
 * 返回值：无
 */
void System_WDT_Feed(void)
{
    // 简化实现，不启用看门狗
}
