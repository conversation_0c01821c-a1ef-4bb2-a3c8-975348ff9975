# STM32 PWM直流电机控制系统

## 项目简介

这是一个基于STM32F103C8的PWM直流电机控制系统，具有多种工作模式和完善的用户界面。

## 主要功能

### 🎯 核心功能
- **PWM电机控制**: 20kHz高频PWM，支持正反转和速度调节
- **旋转编码器**: 实时速度调节，增强型抗干扰算法
- **多工作模式**: 手动、自动、测试、校准四种模式
- **OLED显示**: 多页面显示，实时状态监控
- **按键控制**: 模式切换、页面切换、紧急停止
- **LED指示**: 运行状态和错误指示
- **安全保护**: 温度监控、错误诊断

### 📊 工作模式
1. **手动模式(Manual)**: 通过编码器手动调节电机速度
2. **自动模式(Auto)**: 电机自动往返运行
3. **测试模式(Test)**: 固定速度测试运行
4. **校准模式(Calib)**: 电机自检和校准

### 📱 显示界面
- **主页面**: 显示工作模式、当前速度、目标速度、电机状态
- **状态页面**: 显示温度、光照、系统运行时间
- **设置页面**: 显示错误状态、PWM频率、版本信息

## 硬件连接

### 🔌 引脚定义
```
电机控制:
- PA2: PWM输出
- PA4: 方向控制1
- PA5: 方向控制2

编码器:
- PB0: A相输入
- PB1: B相输入

按键:
- PA11: 按键1 (模式切换)
- PA12: 按键2 (页面切换/紧急停止)

LED指示:
- PA0: LED0 (电机状态)
- PA1: LED1 (系统状态)

其他外设:
- PB6/PB7: OLED显示屏(I2C)
- PB12: 蜂鸣器
- PB13: 光敏传感器
- PB14: DS18B20温度传感器
```

### ⚡ 电机驱动电路
推荐使用L298N或类似的H桥驱动芯片：
- IN1 -> PA4
- IN2 -> PA5  
- ENA -> PA2 (PWM)

## 软件架构

### 📁 目录结构
```
├── Hardware/          # 硬件驱动层
│   ├── Config.h       # 统一配置文件
│   ├── Motor.c/h      # 电机控制
│   ├── PWM.c/h        # PWM生成
│   ├── Encoder.c/h    # 编码器处理
│   ├── Key.c/h        # 按键处理
│   ├── LED.c/h        # LED控制
│   ├── OLED.c/h       # OLED显示
│   ├── Buzzer.c/h     # 蜂鸣器控制
│   └── ...
├── System/            # 系统层
│   ├── Delay.c/h      # 延时和系统函数
│   └── System.c/h     # 系统管理
├── User/              # 用户应用层
│   └── main.c         # 主程序
└── Library/           # STM32标准库
```

### 🏗️ 代码特点
- **模块化设计**: 各功能独立封装，便于维护
- **统一配置**: 所有硬件参数集中管理
- **错误处理**: 完善的错误检测和处理机制
- **中文注释**: 详细的中文注释，便于理解
- **性能优化**: 高效的算法和资源利用

## 使用说明

### 🚀 快速开始
1. 按照硬件连接图连接电路
2. 编译并下载程序到STM32
3. 上电后系统自动初始化
4. 使用按键和编码器控制电机

### 🎮 操作方法
- **按键1短按**: 切换工作模式
- **按键2短按**: 切换显示页面
- **按键2长按**: 紧急停止
- **旋转编码器**: 调节电机速度(手动模式)

### 📈 参数配置
在`Hardware/Config.h`中可以修改：
- PWM频率和分辨率
- 电机速度范围和步长
- 按键消抖时间
- LED闪烁周期
- 各种引脚定义

## 技术规格

- **MCU**: STM32F103C8T6
- **时钟**: 72MHz
- **PWM频率**: 20kHz
- **速度范围**: -100 ~ +100
- **编码器分辨率**: 可配置
- **显示刷新**: 100ms
- **按键响应**: 20ms消抖

## 安全特性

- **过载保护**: 电机过载检测
- **温度监控**: 实时温度监控和保护
- **错误诊断**: 多种错误状态检测
- **紧急停止**: 长按按键2紧急停止
- **看门狗**: 系统死锁保护(可选)

## 开发环境

- **IDE**: Keil MDK-ARM 5.x
- **编译器**: ARMCC
- **调试器**: ST-Link
- **标准库**: STM32F10x_StdPeriph_Lib

## 版本历史

- **v1.0**: 基础PWM电机控制
- **v1.1**: 添加多工作模式和完善界面
- **v1.2**: 优化编码器算法和错误处理

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---
*最后更新: 2024年*
