#include "stm32f10x.h"
#include "Key.h"
#include "Config.h"
#include "Delay.h"

/**
 * 函数：按键初始化
 * 参数：无
 * 返回值：无
 */
void Key_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // GPIO初始化
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitStructure.GPIO_Pin = KEY1_PIN | KEY2_PIN;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(KEY_PORT, &GPIO_InitStructure);
}

/**
 * 函数：按键获取键码(非阻塞)
 * 参数：无
 * 返回值：按下按键的键码值，范围：0~2，返回0代表没有按键按下
 */
uint8_t Key_GetNum(void)
{
    static uint8_t key_state[2] = {1, 1};       // 按键状态记录
    static uint32_t key_time[2] = {0, 0};       // 按键时间记录
    uint8_t key_current[2];
    uint8_t KeyNum = 0;
    uint32_t current_tick = System_GetTick();

    // 读取当前按键状态
    key_current[0] = GPIO_ReadInputDataBit(KEY_PORT, KEY1_PIN);
    key_current[1] = GPIO_ReadInputDataBit(KEY_PORT, KEY2_PIN);

    // 检查按键1
    if(key_state[0] == 1 && key_current[0] == 0) {
        key_time[0] = current_tick;
        key_state[0] = 0;
    }
    else if(key_state[0] == 0 && key_current[0] == 1) {
        if((current_tick - key_time[0]) >= KEY_DEBOUNCE_TIME) {
            KeyNum = 1;
        }
        key_state[0] = 1;
    }

    // 检查按键2
    if(key_state[1] == 1 && key_current[1] == 0) {
        key_time[1] = current_tick;
        key_state[1] = 0;
    }
    else if(key_state[1] == 0 && key_current[1] == 1) {
        if((current_tick - key_time[1]) >= KEY_DEBOUNCE_TIME) {
            KeyNum = 2;
        }
        key_state[1] = 1;
    }

    return KeyNum;
}

/**
 * 函数：检查按键是否按下(实时状态)
 * 参数：key_num - 按键号(1或2)
 * 返回值：1-按下, 0-未按下
 */
uint8_t Key_IsPressed(uint8_t key_num)
{
    if(key_num == 1) {
        return (GPIO_ReadInputDataBit(KEY_PORT, KEY1_PIN) == 0) ? 1 : 0;
    }
    else if(key_num == 2) {
        return (GPIO_ReadInputDataBit(KEY_PORT, KEY2_PIN) == 0) ? 1 : 0;
    }
    return 0;
}
