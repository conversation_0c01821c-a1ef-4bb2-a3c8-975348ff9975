#ifndef __SYSTEM_H
#define __SYSTEM_H

#include "stm32f10x.h"
#include "Config.h"

//=============================================================================
// 系统初始化函数
//=============================================================================
void System_Init(void);                    // 系统初始化
void System_Clock_Init(void);              // 系统时钟初始化
void System_NVIC_Init(void);               // 中断优先级分组初始化
void System_SysTick_Init(void);            // SysTick初始化

//=============================================================================
// 系统状态管理
//=============================================================================
SystemStatus_t System_GetStatus(void);     // 获取系统状态
void System_SetError(uint8_t error_code);  // 设置错误状态
void System_ClearError(uint8_t error_code);// 清除错误状态
uint8_t System_GetError(void);             // 获取错误状态
void System_Reset(void);                   // 系统复位

//=============================================================================
// 系统时间管理
//=============================================================================
uint32_t System_GetTick(void);             // 获取系统滴答计数
void System_Delay_ms(uint32_t ms);         // 毫秒延时
uint8_t System_CheckTimeout(uint32_t start_tick, uint32_t timeout_ms); // 超时检查

//=============================================================================
// 看门狗管理
//=============================================================================
void System_WDT_Init(void);                // 看门狗初始化
void System_WDT_Feed(void);                // 喂狗

//=============================================================================
// 电源管理
//=============================================================================
void System_EnterSleep(void);              // 进入睡眠模式
void System_EnterStop(void);               // 进入停止模式
void System_Wakeup(void);                  // 唤醒系统

#endif
